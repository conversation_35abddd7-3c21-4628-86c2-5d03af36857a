<?php

if (!function_exists('format_indian_currency')) {
    function format_indian_currency($amount)
    {
        $amount = number_format($amount, 2);
        $amount_array = explode('.', $amount);
        $whole = $amount_array[0];
        $fraction = $amount_array[1];

        $formatted = preg_replace("/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/i", "$1,", $whole);
        return $formatted . '.' . $fraction;
    }
}