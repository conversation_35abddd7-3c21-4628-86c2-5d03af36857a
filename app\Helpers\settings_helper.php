<?php

use App\Models\Setting;

if (!function_exists('setting')) {
    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        // save in cache, to avoid repeated calls to db
        return cache()->rememberForever('setting.' . $key, function () use ($key, $default) {
            return Setting::getValue($key, $default);
        });
        // return Setting::getValue($key, $default);
    }
}
