<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class BannerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $banners = Banner::latest()->paginate(10);
        return view('admin.banners.index', compact('banners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.banners.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'shop_now_link' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = 'banner_' . time() . '.' . $image->getClientOriginalExtension();

            // Resize and save image
            $manager = new \Intervention\Image\ImageManager(new \Intervention\Image\Drivers\Gd\Driver());
            $img = $manager->read($image->getRealPath());
            $img->resize(1920, 1280);

            // Create directory if it doesn't exist
            $path = 'public/banners';
            if (!Storage::exists($path)) {
                Storage::makeDirectory($path);
            }

            // Save the image
            Storage::put($path . '/' . $filename, (string) $img->encode());

            $imagePath = 'banners/' . $filename;
        }

        Banner::create([
            'name' => $request->name,
            'title' => $request->title,
            'description' => $request->description,
            'shop_now_link' => $request->shop_now_link,
            'status' => $request->status,
            'image_link' => $imagePath ?? '',
        ]);

        return redirect()->route('banners.index')
            ->with('success', 'Banner created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Banner $banner)
    {
        return view('admin.banners.show', compact('banner'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Banner $banner)
    {
        return view('admin.banners.edit', compact('banner'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Banner $banner)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'shop_now_link' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload if a new image is provided
        if ($request->hasFile('image')) {
            // Delete old image
            if ($banner->image_link && Storage::exists('public/' . $banner->image_link)) {
                Storage::delete('public/' . $banner->image_link);
            }

            $image = $request->file('image');
            $filename = 'banner_' . time() . '.' . $image->getClientOriginalExtension();

            // Resize and save image
            $manager = new \Intervention\Image\ImageManager(new \Intervention\Image\Drivers\Gd\Driver());
            $img = $manager->read($image->getRealPath());
            $img->resize(1920, 1280, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });

            // Create directory if it doesn't exist
            $path = 'public/banners';
            if (!Storage::exists($path)) {
                Storage::makeDirectory($path);
            }

            // Save the image
            Storage::put($path . '/' . $filename, (string) $img->encode());

            $imagePath = 'banners/' . $filename;
            $banner->image_link = $imagePath;
        }

        $banner->name = $request->name;
        $banner->title = $request->title;
        $banner->description = $request->description;
        $banner->shop_now_link = $request->shop_now_link;
        $banner->status = $request->status;
        $banner->save();

        return redirect()->route('banners.index')
            ->with('success', 'Banner updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Banner $banner)
    {
        // Delete the image file
        if ($banner->image_link && Storage::exists('public/' . $banner->image_link)) {
            Storage::delete('public/' . $banner->image_link);
        }

        $banner->delete();

        return redirect()->route('banners.index')
            ->with('success', 'Banner deleted successfully.');
    }
}

