<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $query = Category::with('parent');

        // Search
        if ($request->has('search')) {
            $searchTerm = $request->search;
            $query->where('name', 'LIKE', "%{$searchTerm}%");
        }

        // Filter by status
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        // Filter by parent category
        if ($request->has('parent_id') && $request->parent_id != '') {
            $query->where('parent_id', $request->parent_id);
        }

        // Sort
        $sort = $request->input('sort', 'created_at');
        $direction = $request->input('direction', 'desc');
        $query->orderBy($sort, $direction);

        // Paginate
        $perPage = $request->input('per_page', 10);
        $categories = $query->paginate($perPage)->withQueryString();

        // Get parent categories for filter dropdown
        $parentCategories = Category::whereNull('parent_id')->pluck('name', 'id');

        return view('admin.categories.index', compact('categories', 'parentCategories'));
    }

    public function create()
    {
        $categories = Category::where('parent_id', null)->get();
        return view('admin.categories.create', compact('categories'));
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|max:255|unique:categories,name',
                'slug' => 'nullable|max:255|unique:categories,slug',
                'description' => 'nullable|string',
                'parent_id' => 'nullable|exists:categories,id',
                'status' => 'required|in:active,inactive',
                'images' => 'required|array',
                'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
                'primary_image' => 'required|integer|min:0',
            ]);

            DB::beginTransaction();

            $category = Category::create([
                'name' => $validated['name'],
                'slug' => $validated['slug'],
                'description' => $validated['description'],
                'parent_id' => $validated['parent_id'],
                'status' => $validated['status'],
            ]);

            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $imageFile) {
                    // Store the original image
                    $path = $imageFile->store('categories', 'public');
                    $fullPath = Storage::disk('public')->path($path);

                    // Resize the image to match the exact dimensions (300x300 pixels)
                    try {
                        $manager = new ImageManager(new Driver());
                        $image = $manager->read($fullPath);
                        $image->resize(300, 300);
                        $image->save($fullPath);

                        $category->images()->create([
                            'path' => $path,
                            'original_name' => $imageFile->getClientOriginalName(),
                            'mime_type' => $imageFile->getMimeType(),
                            'size' => $imageFile->getSize(),
                            'is_primary' => $index == $request->input('primary_image'),
                        ]);
                    } catch (\Exception $e) {
                        // Log the error but don't throw an exception
                        \Log::error('Image resize error: ' . $e->getMessage());

                        // Still save the original image
                        $category->images()->create([
                            'path' => $path,
                            'original_name' => $imageFile->getClientOriginalName(),
                            'mime_type' => $imageFile->getMimeType(),
                            'size' => $imageFile->getSize(),
                            'is_primary' => $index == $request->input('primary_image'),
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully',
                'redirect' => route('categories.index')
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Category creation error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error creating category: ' . $e->getMessage()
            ], 500);
        }
    }

    public function edit(Category $category)
    {
        $categories = Category::where('parent_id', null)
            ->where('id', '!=', $category->id)
            ->get();
        return view('admin.categories.edit', compact('category', 'categories'));
    }

    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|max:255',
            'description' => 'nullable|string',
            'parent_id' => [
                'nullable',
                'exists:categories,id',
                function ($attribute, $value, $fail) use ($category) {
                    if ($value == $category->id) {
                        $fail('A category cannot be its own parent.');
                    }
                },
            ],
            'status' => 'required|in:active,inactive',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'primary_image' => 'nullable',
            'remove_images' => 'nullable',
        ]);

        $category->update($validated);

        // Handle image deletions
        if ($request->input('remove_images')) {
            $removeImageIds = explode(',', $request->input('remove_images'));
            $category->images()->whereIn('id', $removeImageIds)->delete();
        }

        // Handle new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $imageFile) {
                // Store the original image
                $path = $imageFile->store('categories', 'public');
                $fullPath = Storage::disk('public')->path($path);

                $isPrimary = $request->input('primary_image') === 'new_' . $index;

                // Resize the image to match the exact dimensions (300x300 pixels)
                try {
                    $manager = new ImageManager(new Driver());
                    $image = $manager->read($fullPath);
                    $image->resize(300, 300);
                    $image->save($fullPath);

                    $category->images()->create([
                        'path' => $path,
                        'original_name' => $imageFile->getClientOriginalName(),
                        'mime_type' => $imageFile->getMimeType(),
                        'size' => $imageFile->getSize(),
                        'is_primary' => $isPrimary,
                    ]);
                } catch (\Exception $e) {
                    // Log the error but don't throw an exception
                    \Log::error('Image resize error: ' . $e->getMessage());

                    // Still save the original image
                    $category->images()->create([
                        'path' => $path,
                        'original_name' => $imageFile->getClientOriginalName(),
                        'mime_type' => $imageFile->getMimeType(),
                        'size' => $imageFile->getSize(),
                        'is_primary' => $isPrimary,
                    ]);
                }
            }
        }

        // Update primary image for existing images
        if (is_numeric($request->input('primary_image'))) {
            $category->images()->update(['is_primary' => false]);
            $category->images()->where('id', $request->input('primary_image'))->update(['is_primary' => true]);
        }

        return redirect()
            ->route('categories.index')
            ->with('success', 'Category updated successfully.');
    }

    public function destroy(Category $category)
    {
        // Delete physical image files from storage
        foreach ($category->images as $image) {
            Storage::disk('public')->delete($image->path);
        }

        // Delete CKEditor images from the description
        if ($category->description) {
            preg_match_all('/<img[^>]+src="([^">]+)"/', $category->description, $matches);

            if (!empty($matches[1])) {
                foreach ($matches[1] as $src) {
                    // Convert URL to storage path
                    $path = str_replace(url('storage/'), '', $src);
                    if (str_starts_with($path, '/')) {
                        $path = substr($path, 1);
                    }

                    if (str_contains($path, 'ckeditor/')) {
                        Storage::disk('public')->delete($path);
                    }
                }
            }
        }

        // Delete the category (this will also delete related images from database due to cascade)
        $category->delete();

        return redirect()
            ->route('categories.index')
            ->with('success', 'Category deleted successfully.');
    }
}




