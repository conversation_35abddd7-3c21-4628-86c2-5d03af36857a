<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Listing;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class ListingController extends Controller
{
    public function index(Request $request)
    {
        $query = Listing::query();

        // Search
        if ($search = $request->input('search')) {
            $query->where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%");
        }

        // Status filter
        if ($status = $request->input('status')) {
            $query->where('status', $status);
        }

        // Sort
        $sort = $request->input('sort', 'position');
        $direction = $request->input('direction', 'asc');
        $query->orderBy($sort, $direction);

        $listings = $query->paginate(10)->withQueryString();

        return view('admin.listings.index', compact('listings'));
    }

    public function create()
    {
        return view('admin.listings.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|max:255',
            'slug' => 'nullable|max:255|unique:listings,slug',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
            'position' => 'nullable|integer|min:1',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        if (empty($validated['position'])) {
            $validated['position'] = Listing::max('position') + 1;
        }

        Listing::create($validated);

        return redirect()
            ->route('listings.index')
            ->with('success', 'Listing created successfully.');
    }

    public function edit(Listing $listing)
    {
        $products = Product::orderBy('name')->get();
        // Convert to collection instead of array
        $selectedProducts = collect($listing->products()->pluck('products.id'));

        return view('admin.listings.edit', compact('listing', 'products', 'selectedProducts'));
    }

    public function update(Request $request, Listing $listing)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|max:255',
                'slug' => 'nullable|max:255|unique:listings,slug,' . $listing->id,
                'description' => 'nullable|string',
                'status' => 'required|in:active,inactive',
                'position' => 'nullable|integer|min:1',
                'products' => 'nullable|array',
                'products.*' => 'exists:products,id',
                'product_positions' => 'nullable|array',
                'product_positions.*' => 'integer|min:0',
            ]);

            DB::beginTransaction();

            // Update listing
            if (empty($validated['slug'])) {
                $validated['slug'] = Str::slug($validated['name']);
            }

            $listing->update($validated);

            // Sync products with positions
            $products = [];
            if (!empty($validated['products'])) {
                foreach ($validated['products'] as $index => $productId) {
                    $position = $validated['product_positions'][$index] ?? 0;
                    $products[$productId] = ['position' => $position];
                }
                $listing->products()->sync($products);
            } else {
                $listing->products()->detach();
            }

            DB::commit();

            return redirect()
                ->route('listings.index')
                ->with('success', 'Listing updated successfully.');

        } catch (ValidationException $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->withErrors($e->errors());

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Listing update error: ' . $e->getMessage());

            return back()
                ->withInput()
                ->with('error', 'Error updating listing: ' . $e->getMessage());
        }
    }

    public function destroy(Listing $listing)
    {
        $listing->delete();

        return redirect()
            ->route('listings.index')
            ->with('success', 'Listing deleted successfully.');
    }
}
