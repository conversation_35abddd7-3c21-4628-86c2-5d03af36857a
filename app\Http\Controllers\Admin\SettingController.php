<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SettingController extends Controller
{
    /**
     * Display a listing of the settings.
     */
    public function index()
    {
        $settings = Setting::latest()->paginate(10);
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Show the form for creating a new setting.
     */
    public function create()
    {
        $types = Setting::getTypes();
        return view('admin.settings.create', compact('types'));
    }

    /**
     * Store a newly created setting in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'label' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:settings,key',
            'type' => ['required', Rule::in(array_keys(Setting::getTypes()))],
            'value' => 'nullable',
        ]);

        // Handle image uploads
        if ($validated['type'] === 'image' && $request->hasFile('image_upload')) {
            $path = $request->file('image_upload')->store('settings', 'public');
            $validated['value'] = $path;
        }

        $validated['updated_by'] = Auth::id();

        Setting::create($validated);

        return redirect()->route('settings.index')
            ->with('success', 'Setting created successfully.');
    }

    /**
     * Show the form for editing the specified setting.
     */
    public function edit(Setting $setting)
    {
        $types = Setting::getTypes();
        return view('admin.settings.edit', compact('setting', 'types'));
    }

    /**
     * Update the specified setting in storage.
     */
    public function update(Request $request, Setting $setting)
    {
        $validated = $request->validate([
            'label' => 'required|string|max:255',
            'key' => [
                'required',
                'string',
                'max:255',
                Rule::unique('settings')->ignore($setting->id),
            ],
            'type' => ['required', Rule::in(array_keys(Setting::getTypes()))],
            'value' => 'nullable',
        ]);

        // Handle image uploads
        if ($validated['type'] === 'image') {
            if ($request->hasFile('image_upload')) {
                // Delete old image if it exists
                if ($setting->value && file_exists(storage_path('app/public/' . $setting->value))) {
                    unlink(storage_path('app/public/' . $setting->value));
                }

                $path = $request->file('image_upload')->store('settings', 'public');
                $validated['value'] = $path;
            } else {
                // Keep the existing image if no new one is uploaded
                $validated['value'] = $setting->value;
            }
        }

        $validated['updated_by'] = Auth::id();

        $setting->update($validated);

        cache()->forget('setting.' . $validated['key']);

        return redirect()->route('settings.index')
            ->with('success', 'Setting updated successfully.');
    }

    /**
     * Remove the specified setting from storage.
     */
    public function destroy(Setting $setting)
    {
        // Delete associated image if it exists
        if ($setting->type === 'image' && $setting->value) {
            $filePath = storage_path('app/public/' . $setting->value);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        $setting->delete();

        return redirect()->route('settings.index')
            ->with('success', 'Setting deleted successfully.');
    }
}
