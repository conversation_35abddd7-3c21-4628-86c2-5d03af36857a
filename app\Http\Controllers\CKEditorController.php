<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CKEditorController extends Controller
{
    public function upload(Request $request)
    {
        if ($request->hasFile('upload')) {
            $file = $request->file('upload');
            
            // Validate file
            $request->validate([
                'upload' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            // Generate unique filename
            $fileName = uniqid() . '_' . $file->getClientOriginalName();
            
            // Store file
            $path = $file->storeAs('ckeditor', $fileName, 'public');
            
            // Return response
            return response()->json([
                'url' => Storage::url($path)
            ]);
        }
        
        return response()->json([
            'error' => [
                'message' => 'No file was uploaded.'
            ]
        ], 400);
    }
}