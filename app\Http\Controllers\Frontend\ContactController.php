<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Enquiry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\EnquiryMail;

class ContactController extends Controller
{
    public function index()
    {
        return view('pages.contact');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'product_id' => 'nullable|exists:products,id'
        ]);

        // Store enquiry in database
        $enquiry = Enquiry::create($validated);

        // Send email notification
        Mail::to(config('mail.admin_email'))->send(new EnquiryMail($enquiry));

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your enquiry. We will contact you soon!'
        ]);
    }
}