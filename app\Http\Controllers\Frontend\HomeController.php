<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use App\Models\Product;
use App\Models\Category;
use App\Models\Listing;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get featured products
        $featuredProducts = Product::with([
            'images' => function ($query) {
                $query->where('is_primary', true);
            }
        ])
            ->take(8)
            ->get();

        // Get trending products
        $trendingProducts = Product::with([
            'images' => function ($query) {
                $query->where('is_primary', true);
            }
        ])
            ->inRandomOrder()
            ->take(8)
            ->get();

        // Get categories for the featured categories section
        $categories = Category::where('status', 'active')
            ->withCount('products')
            ->orderBy('products_count', 'desc')
            ->with([
                'images' => function ($query) {
                    $query->where('is_primary', true);
                }
            ])
            ->get();

        // Get active listings with their products
        $listings = Listing::where('status', 'active')
            ->orderBy('position')
            ->with([
                'products' => function ($query) {
                    $query->with([
                        'images' => function ($imageQuery) {
                            $imageQuery->where('is_primary', true);
                        }
                    ]);
                }
            ])
            ->get();

        // Get active banners
        $banners = Banner::where('status', 'active')
            ->latest()
            ->get();

        return view('pages.home', compact('featuredProducts', 'trendingProducts', 'categories', 'listings', 'banners'));
    }

    public function categoryProducts($slug, Request $request)
    {
        $category = Category::where('slug', $slug)->firstOrFail();

        $query = $category->products()
            ->with([
                'images' => function ($query) {
                    $query->where('is_primary', true);
                }
            ]);

        // Apply sorting
        $sort = $request->input('sort', 'default');

        switch ($sort) {
            case 'latest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'bestseller':
                // Assuming you have a sales_count or similar field
                // If not, you can remove this option or implement a different logic
                $query->orderBy('views', 'desc');
                break;
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            default:
                // Default sorting (you can define your default sort here)
                $query->orderBy('created_at', 'desc');
                break;
        }

        $products = $query->paginate(12)->withQueryString();

        return view('pages.category', compact('category', 'products', 'sort'));
    }

    public function productView($slug)
    {
        $product = Product::where('slug', $slug)
            ->with([
                'images', // Load all images
                'categories'
            ])
            ->firstOrFail();

        // Get related products from the same categories
        $categoryIds = $product->categories->pluck('id');

        $relatedProducts = Product::whereHas('categories', function ($query) use ($categoryIds) {
            $query->whereIn('categories.id', $categoryIds);
        })
            ->where('id', '!=', $product->id) // Exclude current product
            ->with([
                'images' => function ($query) {
                    $query->where('is_primary', true);
                }
            ])
            ->inRandomOrder() // Randomize the results
            ->take(4) // Limit to 4 related products
            ->get();

        return view('pages.product', compact('product', 'relatedProducts'));
    }

    public function listingProducts($slug, Request $request)
    {
        $listing = Listing::where('slug', $slug)
            ->where('status', 'active')
            ->firstOrFail();

        // Get products from the listing with pagination
        $query = $listing->products()
            ->with([
                'images' => function ($query) {
                    $query->where('is_primary', true);
                }
            ]);

        // Apply sorting
        $sort = $request->input('sort', 'default');

        switch ($sort) {
            case 'latest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'bestseller':
                $query->orderBy('views', 'desc');
                break;
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            default:
                // Default sorting by position in the listing
                $query->orderBy('listing_product.position', 'asc');
                break;
        }

        $products = $query->paginate(12)->withQueryString();

        return view('pages.listing', compact('listing', 'products', 'sort'));
    }
}