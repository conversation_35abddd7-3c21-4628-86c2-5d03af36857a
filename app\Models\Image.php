<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Image extends Model
{
    protected $fillable = [
        'path',
        'original_name',
        'mime_type',
        'size',
        'is_primary'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'size' => 'integer'
    ];

    public function imageable(): MorphTo
    {
        return $this->morphTo();
    }
}
