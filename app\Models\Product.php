<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Product extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'youtube_video_url',
        'price'
    ];

    protected $casts = [
        'price' => 'decimal:2'
    ];

    protected $appends = ['primary_image'];

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_product')
            ->withTimestamps();
    }

    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable');
    }

    // Optional: Helper method to get primary image
    public function getPrimaryImageAttribute()
    {
        return $this->images()->where('is_primary', true)->first();
    }

    public function listings(): BelongsToMany
    {
        return $this->belongsToMany(Listing::class)
            ->withPivot('position')
            ->orderBy('listing_product.position')
            ->withTimestamps();
    }
}
