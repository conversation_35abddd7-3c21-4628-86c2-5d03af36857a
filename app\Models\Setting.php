<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'label',
        'key',
        'type',
        'value',
        'options',
        'updated_by',
    ];

    protected $casts = [
        'options' => 'array',
    ];

    /**
     * Get the user who last updated this setting.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $setting = self::where('key', $key)->first();

        return $setting ? $setting->value : $default;
    }

    /**
     * Set a setting value by key
     *
     * @param string $key
     * @param mixed $value
     * @param int|null $userId
     * @return bool
     */
    public static function setValue(string $key, $value, $userId = null)
    {
        $setting = self::where('key', $key)->first();

        if ($setting) {
            return $setting->update([
                'value' => $value,
                'updated_by' => $userId,
            ]);
        }

        return false;
    }

    /**
     * Get available setting types
     *
     * @return array
     */
    public static function getTypes(): array
    {
        return [
            'text' => 'Text',
            'textarea' => 'Textarea',
            'url' => 'URL',
            'image' => 'Image Upload'
        ];
    }

    /**
     * Get the formatted value based on the setting type
     *
     * @return mixed
     */
    public function getFormattedValueAttribute()
    {
        switch ($this->type) {
            case 'image':
                return asset('storage/' . $this->value);
            default:
                return $this->value;
        }
    }
}
