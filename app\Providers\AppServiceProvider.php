<?php

namespace App\Providers;

use App\Models\Category;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Share categories with all views
        View::composer('layouts.front', function ($view) {
            $footerCategories = Category::where('status', 'active')
                ->take(7) // Limit to 7 categories for the footer
                ->orderBy('name')
                ->get();

            $view->with('footerCategories', $footerCategories);
        });
    }
}
