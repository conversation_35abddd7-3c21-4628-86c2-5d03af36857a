## Quick start

There are multiple ways to install AdminLTE.

### Download & Changelog:

Always Recommended to download from GitHub latest release [AdminLTE 4](https://github.com/ColorlibHQ/AdminLTE/releases/latest) for bug free and latest features.\
Visit the [releases](https://github.com/ColorlibHQ/AdminLTE/releases) page to view the changelog.\
Legacy Releases are [AdminLTE 3](https://github.com/ColorlibHQ/AdminLTE/releases/tag/v3.2.0) / [AdminLTE 2](https://github.com/ColorlibHQ/AdminLTE/releases/tag/v2.4.18) / [AdminLTE 1](https://github.com/ColorlibHQ/AdminLTE/releases/tag/1.3.1).

## Stable release

### Grab from [jsdelivr](https://www.jsdelivr.com/package/npm/admin-lte) CDN:

_**Important Note**: You needed to add separately cdn links for plugins in your project._

```html
<script
  src="https://cdn.jsdelivr.net/npm/admin-lte@4.0.0-beta3/dist/js/adminlte.min.js"
  crossorigin="anonymous"
></script>
```

```html
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/admin-lte@4.0.0-beta3/dist/css/adminlte.min.css"
  crossorigin="anonymous"
/>
```

### Using The Command Line:

_**Important Note**: To install it via npm/Yarn, you need at least Node.js 14 or higher._

#### Via npm

```bash
npm install admin-lte@4.0.0-beta3 --save
```

#### Via Yarn

```bash
yarn add admin-lte@4.0.0-beta3
```

#### Via Composer

```bash
composer require "almasaeed2010/adminlte=4.0.0-beta3"
```

#### Via Git

```bash
git clone https://github.com/ColorlibHQ/AdminLTE.git
```
