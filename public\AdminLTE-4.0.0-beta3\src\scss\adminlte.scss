/*!
 *   AdminLTE v4.0.0-beta3
 *   Author: Colorlib
 *   Website: AdminLTE.io <https://adminlte.io>
 *   License: Open source - MIT <https://opensource.org/licenses/MIT>
 */

// Bootstrap Configuration
// ---------------------------------------------------
@import "bootstrap/scss/functions";

// AdminLTE Configuration
// ---------------------------------------------------
@import "bootstrap-variables"; // little modified are here

// Bootstrap Configuration
// ---------------------------------------------------
@import "bootstrap/scss/variables";
@import "bootstrap/scss/variables-dark";
@import "bootstrap/scss/maps";
@import "bootstrap/scss/mixins";
@import "bootstrap/scss/utilities";

// Bootstrap Layout & components
@import "bootstrap/scss/root";
@import "bootstrap/scss/reboot";
@import "bootstrap/scss/type";
@import "bootstrap/scss/images";
@import "bootstrap/scss/containers";
@import "bootstrap/scss/grid";
@import "bootstrap/scss/tables";
@import "bootstrap/scss/forms";
@import "bootstrap/scss/buttons";
@import "bootstrap/scss/transitions";
@import "bootstrap/scss/dropdown";
@import "bootstrap/scss/button-group";
@import "bootstrap/scss/nav";
@import "bootstrap/scss/navbar";
@import "bootstrap/scss/card";
@import "bootstrap/scss/accordion";
@import "bootstrap/scss/breadcrumb";
@import "bootstrap/scss/pagination";
@import "bootstrap/scss/badge";
@import "bootstrap/scss/alert";
@import "bootstrap/scss/progress";
@import "bootstrap/scss/list-group";
@import "bootstrap/scss/close";
@import "bootstrap/scss/toasts";
@import "bootstrap/scss/modal";
@import "bootstrap/scss/tooltip";
@import "bootstrap/scss/popover";
@import "bootstrap/scss/carousel";
@import "bootstrap/scss/spinners";
@import "bootstrap/scss/offcanvas";
@import "bootstrap/scss/placeholders";

// Bootstrap Helpers
@import "bootstrap/scss/helpers";

// Bootstrap Utilities
@import "bootstrap/scss/utilities/api";

// AdminLTE Configuration
// ---------------------------------------------------
@import "variables";
@import "variables-dark";
@import "mixins";

// AdiminLTE Parts
// ---------------------------------------------------
@import "parts/core";
@import "parts/components";
@import "parts/extra-components";
@import "parts/pages";
@import "parts/miscellaneous";
