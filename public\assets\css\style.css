/*=====================================================
Template Name   : Fameo
Description     : Furniture Store HTML5 Template
Author          : LunarTemp
Version         : 1.0
=======================================================*/


/*=====================================================
Table of contents
=======================================================
1. Google fonts
2. Theme variables
3. General css
4. Preloader
5. Theme default css
6. Margin & padding
7. Site title css
8. Theme button
9. Scroll top css
10. Header top css
11. Navbar css
12. Mega menu
13. Multi level dropdown menu
14. Search popup
15. Hero css 
16. Small banner css 
17. Mid banner css 
18. Big banner css 
19. Popup banner css 
20. Category css 
21. Shop css 
22. Shop cart css 
23. Shop checkout css
24. Shop checkout complete css 
25. Shop single css 
26. Compare css 
27. Price range css 
28. Product css 
29. Product list css
30. Modal quick view css 
31. Bs custom css 
32. Countdown css
33. Deal css 
34. Feature css 
35. About css 
36. Counter css 
37. Team css 
38. Play btn
39. Video css 
40. Testimonial css 
41. Brand css 
42. Help css 
43. Invoices css 
44. Faq css 
45. Auth css 
46. User css 
47. Coming soon css 
48. Error css 
49. Terms/privacy css 
50. Mail success css 
51. Blog css 
52. Blog single css
53. Widget sidebar css
54. Pagination css 
55. Breadcrumb css 
56. Contact us css 
57. Gallery css 
58. Affiliate css 
59. Cta css 
60. Process css 
61. Instagram css 
62. Choose css 
63. Newsletter css 
64. Footer css 
65. Home 2 css 
66. Home 3 css 
=======================================================*/



/*====================
1. Google fonts
======================*/

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&amp;family=Roboto:wght@100;300;400;500;700;900&amp;display=swap');


/*====================
2. Theme variables
======================*/

:root {
  --body-font: 'Roboto', sans-serif;
  --heading-font: 'Inter', sans-serif;
  --theme-color: #20202c;
  --theme-color2: #ffc82a;
  --theme-color-light: rgba(88, 151, 251, .08);
  --theme-bg-light: #F5F7FA;
  --body-text-color: #757F95;
  --color-white: #ffffff;
  --color-dark: #1F2125;
  --color-green: #11B76B;
  --color-blue: #0049D0;
  --color-skyblue: #00BFFF;
  --color-yellow: #FBA707;
  --color-gray: #ECECEC;
  --color-red: #F05454;
  --hero-overlay-color: #01060F;
  --slider-arrow-bg: rgba(255, 255, 255, 0.2);
  --box-shadow: 0 0 40px 5px rgb(0 0 0 / 5%);
  --box-shadow2: 0 0 15px rgba(0, 0, 0, 0.17);
  --transition: all .5s ease-in-out;
  --transition2: all .3s ease-in-out;
  --border-info-color: rgba(0, 0, 0, 0.08);
  --border-info-color2: rgba(0, 0, 0, 0.05);
  --border-white-color: rgba(255, 255, 255, 0.12);
  --border-white-color2: rgba(255, 255, 255, 0.07);
  --footer-bg: #222934;
  --footer-text-color: #F5FAFF;
}



/*====================
3. General css
======================*/

*,
*:before,
*:after {
  box-sizing: inherit;
}

* {
  scroll-behavior: inherit !important;
}

html,
body {
  height: auto;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--body-font);
  font-style: normal;
  font-size: 16px;
  font-weight: normal;
  color: var(--body-text-color);
  line-height: 1.8;
}

a {
  color:  var(--color-dark);
  display: inline-block;
}

a,
a:active,
a:focus,
a:hover {
  outline: none;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  text-decoration: none;
}

a:hover {
  color: var(--color-blue);
}

ul {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color:  var(--color-dark);
  margin: 0px;
  font-weight: 600;
  font-family: var(--heading-font);
  line-height: 1.2;
}

h1 {
  font-size: 40px;
}

h2 {
  font-size: 35px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 22px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 16px;
}

p {
  margin: 0px;
}

.img,
img {
  max-width: 100%;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  height: auto;
}

label {
  color: #999;
  cursor: pointer;
  font-weight: 400;
}

*::-moz-selection {
  background: #d6b161;
  color: var(--color-white);
  text-shadow: none;
}

::-moz-selection {
  background: #555;
  color: var(--color-white);
  text-shadow: none;
}

::selection {
  background: #555;
  color: var(--color-white);
  text-shadow: none;
}

*::-moz-placeholder {
  color: #999;
  font-size: 16px;
  opacity: 1;
}

*::placeholder {
  color: #999;
  font-size: 16px;
  opacity: 1;
}



/*====================
4. Preloader
======================*/

.preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  background: var(--color-white);
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-ripple {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.loader-ripple div {
  position: absolute;
  border: 4px solid var(--theme-color);
  opacity: 1;
  border-radius: 50%;
  animation: loader-ripple 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.loader-ripple div:nth-child(2) {
  animation-delay: -0.5s;
}

@keyframes loader-ripple {
  0% {
    top: 36px;
    left: 36px;
    width: 0;
    height: 0;
    opacity: 1;
  }

  100% {
    top: 0px;
    left: 0px;
    width: 72px;
    height: 72px;
    opacity: 0;
  }
}




/*===================
5. Theme default css
======================*/

.ovrflow-hidden {
  overflow: hidden;
}

.position-relative {
  position: relative;
  z-index: 1;
}

.text-right {
  text-align: right;
}

.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.c-pd {
  padding: 0 7rem;
}

.s-pd {
  padding: 0 12rem;
}

.h-100 {
  height: 100%;
}

.h-100vh {
  height: 100vh;
}

.bg {
  background: var(--theme-bg-light);
}



/*====================
6. Margin & padding
======================*/

.py-120 {
  padding: 120px 0;
}

.py-110 {
  padding: 110px 0;
}

.py-100 {
  padding: 100px 0;
}

.py-90 {
  padding: 90px 0;
}

.py-80 {
  padding: 80px 0;
}

.pt-0 {
  padding-top: 0px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-90 {
  padding-top: 90px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-120 {
  padding-bottom: 120px;
}

.mt-0 {
  margin-top: 0px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-70 {
  margin-top: 70px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-90 {
  margin-top: 90px;
}

.mt-100 {
  margin-top: 100px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mb-100 {
  margin-bottom: 100px;
}

.my-120 {
  margin: 120px 0;
}

.my-110 {
  margin: 110px 0;
}

.my-100 {
  margin: 100px 0;
}

.my-90 {
  margin: 90px 0;
}

.my-80 {
  margin: 80px 0;
}




/*====================
7. Site title css
======================*/

.site-heading {
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
}

.site-title-tagline {
  text-transform: uppercase;
  letter-spacing: 4px;
  font-size: 17px;
  font-weight: 700;
  color: var(--theme-color);
  position: relative;
  margin-bottom: 5px;
  display: inline-block;
}

.site-title {
  font-weight: 700;
  text-transform: capitalize;
  font-size: 34px;
  color:  var(--color-dark);
  margin-top: 4px;
}

.site-title span{
  color: var(--theme-color);
}

.site-heading p {
  margin-top: 15px;
}

.site-heading-inline{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}

.site-heading-inline .site-title{
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 25px;
  position: relative;
}

.site-heading-inline .site-title::before{
  content: "";
  position: absolute;
  left: 0;
  bottom: -10px;
  background: var(--theme-bg-light);
  border-radius: 0 50px 50px 0;
  width: 80%;
  height: 100%;
  z-index: -1;
}

.site-heading-inline .site-title::after{
  content: "";
  position: absolute;
  left: 0;
  bottom: -10px;
  width: 20%;
  border-bottom: 3px solid var(--theme-color2);
  z-index: -1;
}

.site-heading-inline .site-title img{
  width: 32px;
}

.site-heading-inline a{
  font-weight: 500;
  color: var(--body-text-color);
}

.site-heading-inline a:hover{
  color: var(--theme-color);
}


@media all and (max-width: 767px) {
  .site-heading-inline .site-title {
    font-size: 19px;
  }

  .site-heading-inline .site-title img{
    width: 25px;
  }
}




/*====================
8. Theme button
======================*/

.theme-btn {
  position: relative;
  font-size: 16px;
  background: var(--theme-color2);
  color: var(--color-dark);
  padding: 10px 20px;
  display: inline-block;
  vertical-align: middle;
  text-transform: capitalize;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  overflow: hidden;
  border: none;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  z-index: 1;
}

.theme-btn::before {
  content: "";
  height: 300px;
  width: 300px;
  background:  var(--color-dark);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%) scale(0);
  transition: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.theme-btn:hover {
  color: var(--color-white);
}

.theme-btn:hover::before {
  transform: translateY(-50%) translateX(-50%) scale(1);
}

.theme-btn i {
  margin-left: 5px;
}

.theme-btn span {
  margin-right: 5px;
}

.theme-btn2{
  background: var(--theme-color);
  color: var(--color-white);
}

.theme-btn2:hover{
  color: var(--color-white);
}



/*====================
9. Scroll top css
======================*/

#scroll-top {
  position: fixed;
  bottom: -20px;
  right: 30px;
  z-index: 99;
  font-size: 20px;
  border: none;
  outline: none;
  border-radius: 50px;
  color: var(--color-white);
  background-color: var(--theme-color);
  cursor: pointer;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  box-shadow: var(--box-shadow2);
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
  z-index: 1;
}

#scroll-top.active{
  opacity: 1;
  visibility: visible;
  bottom: 20px;
}


@media all and (min-width: 768px) and (max-width: 1199px) {
  #scroll-top.active {
    bottom: 100px;
  }
}



/*====================
10. Header top css
======================*/

.header-top {
  padding: 6px 0 9px 0;
  position: relative;
  background: var(--theme-color);
}

.header-top-list li {
  display: inline-block;
  color: var(--color-white);
  font-size: 14px;
  font-weight: 500;
  border-right: 1px solid rgba(255, 255, 255, 0.15);
  padding: 0px 13px 0px 6px;
}

.header-top-list li:last-child{
  border-right: none;
  padding-right: 0;
}

.header-top-list li:first-child{
  padding-left: 0;
}

.header-top-list li a {
  color: var(--color-white);
}

.header-top-list li a:hover{
  color: var(--theme-color2);
}

.header-top-list li i{
  color: var(--theme-color2);
  margin-right: 5px;
}

.header-top-right {
  float: right;
}

.header-top-right .dropdown-menu{
  border: none;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  padding: 10px;
  min-width: 80px;
}

.header-top-right .dropdown-item{
  color: var(--color-dark);
  border-radius: 10px;
  text-align: center;
}

.header-top-right .dropdown-item:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.header-top-social a{
  margin-left: 10px;
}

.header-top-social span{
  color: var(--color-white);
}

.header-top-list li .header-top-social a i{
  margin-right: 0;
  transition: var(--transition);
}

.header-top-social a:hover i{
  color: var(--color-dark);
}

@media all and (max-width: 991px) {
  .header-top-list li{
    border-right: none;
  }

  .header-top-left .header-top-list li.help{
    display: none;
  }

  .header-top-right .header-top-list li{
    padding-right: 0px;
  }
  
  .header-top .dropdown-toggle::after{
    float: unset;
  }

  .header-top-social span{
    display: none;
  }
}

@media all and (max-width: 767px) {
  .header-top{
    text-align: center;
  }

  .header-top-right{
    float: unset;
  }

  .header-top-social{
    display: none;
  }
}



/*====================
11. Navbar css
======================*/

.navbar {
  background: transparent;
  padding-top: 0px;
  padding-bottom: 0px;
  z-index: 2;
}

.navbar.fixed-top {
  background: var(--color-white);
  box-shadow: var(--box-shadow2);
  animation: slide-down 0.7s;
}

@keyframes slide-down {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0);
  }
}

.navbar .navbar-brand .logo-display {
  display: block;
}

.navbar .navbar-brand .logo-scrolled {
  display: none;
}

.navbar.fixed-top .navbar-brand .logo-display {
  display: none;
}

.navbar.fixed-top .navbar-brand .logo-scrolled {
  display: block;
}

.navbar .navbar-toggler:focus {
  outline: none;
  box-shadow: none;
}

.navbar-toggler-mobile-icon {
  display: inline-block;
  width: inherit;
  height: inherit;
}

.navbar-brand {
  margin-right: 0;
}

.navbar-brand img {
  width: 180px;
}

.navbar .dropdown-toggle::after {
  display: inline-block;
  margin-left: 5px;
  vertical-align: baseline;
  font-family: 'Font Awesome 6 Pro';
  content: "\f107";
  font-weight: 600;
  border: none;
  font-size: 14px;
}

@media all and (min-width: 992px) {

  .navbar .nav-item .nav-link {
    margin-right: 25px;
    padding: 28px 0 28px 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--color-dark);
    text-transform: capitalize;
  }

  .navbar .nav-item:last-child .nav-link {
    margin-right: 0 !important;
  }

  .navbar.fixed-top .nav-item .nav-link {
    color:  var(--color-dark);
    padding: 22px 0 22px 0;
  }

  .navbar .nav-item .dropdown-menu {
    display: block;
    opacity: 0;
    visibility: hidden;
    transition: .3s;
    margin-top: 0;
    border: none;
    left: -15px;
    border-radius: 15px;
    padding: 15px 12px;
    background: var(--color-white);
    width: 220px;
    box-shadow: var(--box-shadow);
  }

  .navbar .nav-item .dropdown-menu .dropdown-item {
    font-size: 16px;
    font-weight: 400;
    color:  var(--color-dark);
    position: relative;
    overflow: hidden;
    text-transform: capitalize;
    transition: var(--transition);
  }

  .navbar .nav-item .dropdown-menu .dropdown-item:hover {
    background: transparent;
    color: var(--theme-color);
    padding-left: 22px;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item::before {
    content: "\f111";
    position: absolute;
    font-family: "Font Awesome 6 pro";
    left: 8px;
    top: 14px;
    color: var(--theme-color);
    font-weight: bold;
    font-size: 6px;
    transform: scale(0, 0);
    transition: var(--transition);
    z-index: -1;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item:hover::before {
    opacity: 1;
    visibility: visible;
    transform: scale(1, 1);
  }

  .navbar .nav-item .nav-link {
    position: relative;
  }

  .navbar .nav-item .nav-link.active,
  .navbar .nav-item:hover .nav-link {
    color: var(--theme-color);
  }

  .navbar.fixed-top .nav-item .nav-link.active,
  .navbar.fixed-top .nav-item:hover .nav-link {
    color: var(--theme-color);
  }

  .navbar .nav-item:hover .dropdown-menu {
    transition: .3s;
    opacity: 1;
    visibility: visible;
    top: 100%;
    transform: rotateX(0deg);
  }

  .navbar .dropdown-menu-end {
    right: 0;
    left: auto;
  }

  .navbar .dropdown-menu.fade-down {
    top: 80%;
    transform: rotateX(-75deg);
    transform-origin: 0% 0%;
  }

  .navbar .dropdown-menu.fade-up {
    top: 140%;
  }

  /* nav right */
  .nav-right {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
  }

  .nav-right-link {
    position: relative;
    font-size: 16px;
    color: var(--color-dark);
    font-weight: 500;
  }

  .nav-right-link:hover {
    transition: var(--transition);
    color: var(--color-white) !important;
  }

  .navbar.fixed-top .nav-right-link{
    color: var(--color-dark);
  }

  .navbar.fixed-top .nav-right-link:hover{
    color: var(--theme-color) !important;
  }

  /* nav-right-list */
  .nav-right-list li{
    display: inline-block;
  }
  
  .nav-right-list li .list-link{
    position: relative;
    width: 40px;
    color: var(--theme-color);
    font-size: 20px;
    text-align: center;
  }
  
  .nav-right-list .list-link span{
    position: absolute;
    width: 18px;
    height: 18px;
    line-height: 18px;
    color: var(--color-white);
    background: var(--theme-color);
    border-radius: 50px;
    font-size: 12px;
    right: 0px;
    top: -9px;
  }
  
  
  /* dropdown cart */
  .dropdown-cart{
    position: relative;
  }
  
  .dropdown-cart-menu{
    position: absolute;
    right: 0;
    top: 60px;
    background: var(--color-white);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--box-shadow);
    min-width: 320px;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }
  
  .dropdown-cart:hover .dropdown-cart-menu{
    opacity: 1;
    visibility: visible;
    top: 48px;
  }
  
  .dropdown-cart-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-info-color);
  }
  
  .dropdown-cart-header span,
  .dropdown-cart-header a{
    font-size: 13px;
    font-weight: 600;
    color: var(--color-dark);
    text-transform: uppercase;
  }
  
  .dropdown-cart-header a:hover{
    color: var(--theme-color);
  }
  
  .dropdown-cart-list li {
    position: relative;
    width: 100%;
    border-bottom: 1px solid var(--border-info-color);
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  
  .dropdown-cart-list li:last-child{
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
  }
  
  .dropdown-cart-item{
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .dropdown-cart-item .cart-img{
    width: 70px;
    height: 70px;
    border-radius: 10px;
    padding: 6px;
    border: 1px solid var(--border-info-color);
  }
  
  .dropdown-cart-item:hover .cart-img img{
    transform:scale(1.09);
  }
  
  .dropdown-cart-item .cart-info{
    flex: 1;
  }
  
  .dropdown-cart-item .cart-info h4{
    font-size: 14px;
    margin-bottom: 5px;
  }
  
  .dropdown-cart-item .cart-info h4 a:hover{
    color: var(--theme-color);
  }
  
  .dropdown-cart-item .cart-remove{
    color: var(--color-red);
    font-size: 18px;
    transition: var(--transition);
  }
  
  .dropdown-cart-item .cart-remove:hover{
    color: var(--theme-color);
  }
  
  .dropdown-cart-bottom{
    padding-top: 10px;
    margin-top: 15px;
    border-top: 1px solid var(--border-info-color);
  }
  
  .dropdown-cart-total{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .dropdown-cart-total span {
    text-transform: uppercase;
    color: #222;
    font-size: 13px;
    font-weight: 600;
  }
  
  .dropdown-cart-total .total-amount{
    font-size: 14px;
  }
  
  .dropdown-cart-bottom .theme-btn{
    display: block;
    padding: 8px 20px;
    margin-top: 25px;
  }  
}

@media all and (min-width: 1199px) and (max-width: 1399px){
  .navbar .nav-item .nav-link {
    margin-right: 15px !important;
  }

  .navbar.fixed-top .navbar-brand img{
    width: 130px;
  }

  .nav-right {
    margin-left: 15px !important;
  }

  .navbar.fixed-top .nav-right{
    gap: 12px;
  }
}

@media all and (max-width: 1199px) {
  .navbar .nav-right-btn {
    display: none;
  }
}

@media all and (min-width: 992px) and (max-width: 1199px){
  .navbar .nav-item .nav-link {
    margin-right: 15px;
    font-size: 16px;
  }
}


/* mobile menu */
.mobile-menu-right {
  display: none;
}

@media all and (max-width: 991px) {
  .navbar {
    padding-top: 8px;
    padding-bottom: 8px;
    background: var(--color-white);
  }

  .navbar-brand img {
    width: 130px;
  }

  .navbar .offcanvas{
    width: 300px;
  }

  .navbar .offcanvas-header .btn-close{
    box-shadow: none;
  }

  .navbar .offcanvas-brand{
    width: 150px;
  }

  .navbar .dropdown-toggle::after {
    float: right;
    margin-top: 2px;
  }

  .navbar .nav-item .nav-link {
    color:  var(--color-dark);
    font-weight: 500;
    margin-right: 0px;
    transition: var(--transition);
  }

  .navbar .nav-item .nav-link:hover {
    color: var(--theme-color) !important;
  }

  .navbar-toggler {
    padding: 0;
    border: none;
  }

  .navbar-toggler span{
    display: block;
    width: 22px;
    border-bottom: 3px solid var(--theme-color);
    border-radius: 50px;
    margin-top: 5px;
  }

  .navbar-toggler span:first-child{
    margin-top: 0;
  }

  .navbar-toggler span:nth-child(2){
    width: 15px;
  }

  .mobile-menu-right,
  .mobile-menu-btn {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .mobile-menu-right .nav-right-link {
    background: transparent;
    border: none;
    font-size: 20px;
    color: var(--theme-color);
    text-align: center;
    padding: 0;
    position: relative;
  }

  .mobile-menu-right .nav-right-link span{
    position: absolute;
    width: 18px;
    height: 18px;
    line-height: 18px;
    color: var(--color-white);
    background: var(--theme-color);
    border-radius: 50px;
    font-size: 12px;
    right: -10px;
    top: -5px;
  }

  .navbar .dropdown-menu {
    border-radius: 15px;
    border: 1px solid var(--border-info-color);
  }

  .nav-right {
    display: none;
  }
}




/*============================
12. Mega menu
==============================*/

@media all and (min-width: 992px){
  .nav-item.mega-menu {
    position: static;
  }
  
  .navbar .nav-item.mega-menu .dropdown-menu{
    width: 98.2%;
    left: 12px;
  } 

  .mega-menu .mega-content{
    padding: 12px 16px 10px 16px;
  }

  .mega-menu .mega-menu-title{
    font-size: 17px;
    margin-bottom: 15px;
    color: var(--color-dark);
  }

  .mega-menu-img img{
    border-radius: 15px;
  }

  .navbar .nav-item.mega-menu .dropdown-menu .dropdown-item{
    padding-left: 0;
  }

  .navbar .nav-item.mega-menu .dropdown-menu .dropdown-item::before{
    left: 0;
  }

  .navbar .nav-item.mega-menu .dropdown-menu .dropdown-item:hover{
    padding-left: 15px;
  }
}


@media all and (max-width: 991px){
  .mega-menu-img{
    display: none;
  }

  .mega-menu .mega-content h5{
    margin: 15px 0;
  }
}



/*============================
13. Multi level dropdown menu
==============================*/

.navbar .nav-item .dropdown-submenu {
  position: relative;
}

.navbar .nav-item .dropdown-submenu .dropdown-menu::before {
  display: none;
}

.navbar .nav-item .dropdown-submenu a::after {
  transform: rotate(-90deg);
  position: absolute;
  right: 15px;
  top: 6.5px;
  font-weight: 600;
}

.navbar .nav-item .dropdown-submenu a:hover {
  background: transparent;
  color: var(--color-white);
}

.navbar .nav-item .dropdown-submenu .dropdown-menu {
  top: 120%;
  left: 100%;
  opacity: 0;
  visibility: hidden;
}

.navbar .nav-item .dropdown-submenu:hover .dropdown-menu {
  top: 0;
  opacity: 1;
  visibility: visible;
}

/* navbar dropdown-right */
.navbar .dropdown-right .dropdown-submenu .dropdown-menu{
  left: unset;
  right: 100%;
}

@media all and (max-width: 991px) {
  .navbar .nav-item .dropdown-submenu .dropdown-menu {
    margin: 0 17px;
  }

  .navbar .nav-item .dropdown-submenu .dropdown-menu {
    opacity: unset;
    visibility: unset;
  }

  .navbar .nav-item .dropdown-submenu a::after {
    top: 4px;
  }

  .navbar .nav-item .dropdown-submenu a:hover {
    color: var(--theme-color);
  }
}



/* ======================
14. Search popup
====================== */

.search-popup {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100%;
  z-index: 99999;
  margin-top: -540px;
  transform: translateY(-100%);
  background-color: rgba(0, 0, 0, .95);
  transition: all 1500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
  transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
}

.sidenav-bar-visible .search-popup {
  width: 80%;
}

.search-active .search-popup {
  transform: translateY(0%);
  margin-top: 0;
}

.search-popup .close-search {
  position: absolute;
  left: 0;
  right: 0;
  top: 75%;
  border: none;
  margin: 0 auto;
  margin-top: -200px;
  border-radius: 50px;
  text-align: center;
  background: var(--theme-color);
  text-align: center;
  width: 50px;
  height: 50px;
  color: var(--color-white);
  font-size: 20px;
  cursor: pointer;
  box-shadow: var(--box-shadow);
  transition: all 500ms ease;
  opacity: 0;
  visibility: hidden;
}

.search-popup .close-search span{
  transition: var(--transition);
}

.search-popup .close-search:hover span{
  transform: rotate(180deg);
}

.search-active .search-popup .close-search {
  visibility: visible;
  opacity: 1;
  top: 50%;
  transition-delay: 1500ms;
}

.search-popup form {
  position: absolute;
  max-width: 700px;
  top: 50%;
  left: 15px;
  right: 15px;
  margin: -35px auto 0;
  transform: scaleX(0);
  transform-origin: center;
  transition: all 300ms ease;
}

.search-active .search-popup form {
  transform: scaleX(1);
  transition-delay: 1200ms;
}

.search-popup .form-group {
  position: relative;
  margin: 0px;
  overflow: hidden;
}

.search-popup .form-control {
  position: relative;
  width: 100%;
  height: 70px;
  outline: none;
  border-radius: 0px;
  border: none;
  border-bottom: 2px solid var(--color-white);
  background-color: transparent;
  color: var(--color-white);
  font-size: 40px;
  padding: 0 70px 0 20px;
  transition: all 500ms ease;
  text-transform: capitalize;
}

.search-popup .form-control::placeholder{
  color: var(--color-white);
  font-size: 40px;
}


.search-popup .form-group button {
  position: absolute;
  right: 5px;
  top: 5px;
  border-radius: 50px;
  background: transparent;
  text-align: center;
  font-size: 30px;
  color: var(--color-white);
  height: 50px;
  width: 50px;
  border: none;
  cursor: pointer;
  transition: all 500ms ease;
}

.search-popup .form-group button:hover{
  color: var(--theme-color);
}



/*====================
15. Hero css 
======================*/

.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-slider-wrap{
  background: var(--theme-bg-light);
  border-radius: 10px;
}

.hero-single {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-single .hero-content {
  position: relative;
}

.hero-single .hero-content .hero-sub-title {
  display: inline-block;
  color: var(--theme-color2);
  font-size: 22px;
  letter-spacing: 4px;
  font-weight: 700;
  position: relative;
  text-transform: uppercase;
}

.hero-single .hero-content .hero-title {
  color: var(--color-white);
  font-size: 45px;
  font-weight: 700;
  margin: 20px 0;
  text-transform: capitalize;
}

.hero-single .hero-content .hero-title span{
  color: var(--theme-color2);
}

.hero-single .hero-content p {
  color: var(--color-white);
  line-height: 30px;
  font-weight: 400;
  font-size: 17px;
  margin-bottom: 25px;
}

.hero-single .hero-img{
  position: relative;
}

.hero-single .hero-img img{
  width: 75%;
  margin-left: auto;
}

.hero-btn{
  display: flex;
  align-items: center;
  gap: 10px;
}

@media all and (max-width: 991px) {
  .hero-single .hero-img{
    margin-top: 50px;
  }
}


/* hero slider */
.hero-slider{
  position: relative;
}

.hero-slider .owl-dots {
  position: absolute;
  bottom: 28px;
  left: 50%;
  transform: translateX(-50%);
}

.hero-slider .owl-dots .owl-dot {
  background: var(--color-white);
  margin: 5px;
  border-radius: 50px;
  width: 8px;
  height: 8px;
  display: inline-block;
  transition: var(--transition);
}

.hero-slider .owl-dots .owl-dot.active{
  background-color: var(--color-white);
  width: 20px;
}

.hero-slider .owl-nav{
  position: absolute;
  font-size: 25px;
  right: 20px;
  bottom: 16px;
}

.hero-slider .owl-nav button{
  width: 44px;
  height: 44px;
  line-height: 44px;
  background: var(--color-white)!important;
  color: var(--theme-color);
  text-align: center;
  border-radius: 50px;
  transition: var(--transition);
}

.hero-slider .owl-nav button:hover{
  background: var(--theme-color) !important;
  color: var(--color-white);
}

.hero-slider .owl-nav .owl-prev{
  margin-right: 12px;
}

/* hs-1 */
.hs-1 .hero-slider{
  background-image: url(../img/hero/bg.jpg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 20px;
  position: relative;
}

.hs-1 .hero-slider::before{
  content: "";
  position: absolute;
  right: 80px;
  bottom: 0px;
  width: 440px;
  height: 440px;
  background: var(--theme-color2);
  border-radius: 50% 50% 0px 50%;
  transform: rotate(90deg);
}

.hs-1 .hero-single{
  padding: 25px 50px;
}

.hs-1 .hero-single .hero-content .hero-sub-title{
  background: var(--theme-color2);
  color: var(--color-dark);
  font-size: 18px;
  padding: 5px 20px 5px 12px;
  clip-path: polygon(100% 0, 95% 50%, 100% 100%, 0 100%, 0 0) !important;
  border-radius: 50px 0 0 50px;
}

.hs-1 .hero-img-item{
  position: absolute;
  top: 120px;
  left: 150px;
}

.hs-1 .hero-img-content{
  position: absolute;
  width: 230px;
  left: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--color-white);
  padding: 10px;
  margin-top: 10px;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.hs-1 .hero-img-item:hover .hero-img-content{
  opacity: 1;
  visibility: visible;
  margin-top: 0px;
}

.hs-1 .hero-img-content img{
  width: 80px !important;
  height: 80px;
}

.hs-1 .hero-img-info{
  flex: 1;
}

.hs-1 .hero-img-info h6 a{
  color: var(--color-dark);
}

.hs-1 .hero-img-info h6 a:hover{
  color: var(--theme-color);
}

.hs-1 .hero-img-info p{
  margin-top: 5px;
  margin-bottom: 5px;
}

.hs-1 .hero-img-info p del{
  margin-right: 8px;
}

.hs-1 .hero-img-info p span{
  color: var(--color-red);
  font-weight: 500;
}

.hs-1 .hero-img-info .theme-btn{
  padding: 3px 12px;
}

.hs-1 .hero-img-item button{
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: var(--color-white);
  border-radius: 50px;
  text-align: center;
  color: var(--theme-color);
  font-size: 25px;
  border: none;
  animation: hero-pulse 2s infinite;
}

@keyframes hero-pulse {
  0%{
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 1);
  }

  100%{
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }
}


@media all and (max-width: 1199px) {
  .hs-1 .hero-single{
    padding: 30px 30px;
  }

  .hs-1 .hero-slider::before {
    width: 340px;
    height: 340px;
  }
}

@media all and (max-width: 991px) {
  .hs-1 .hero-single{
    padding: 50px;
  }

  .hs-1 .hero-single .hero-img img{
    width: 100%;
  }

  .hs-1 .hero-slider::before {
    width: 550px;
    height: 550px;
  }
}

@media all and (max-width: 767px) {
  .hs-1 .hero-single{
    padding: 40px 20px 60px 20px;
  }

  .hs-1 .hero-slider::before {
    right: 10px;
    bottom: 0px;
    width: 300px;
    height: 300px;
  }

  .hs-1 .hero-img-item{
    top: 60px;
    left: 10px;
  }
}


/* hs-2 */
.hs-2.hero-section{
  margin-top: -6rem;
  background: var(--theme-color2);
}

.hs-2::before{
  content: "";
  position: absolute;
  right: 10%;
  top: 0;
  width: 400px;
  height: 500px;
  border-radius: 0 0 100px 100px;
  background: var(--color-yellow);
  opacity: .4;
}

.hs-2 .hero-single{
  padding-top: 140px;
  padding-bottom: 100px;
}

.hs-2 .hero-single .hero-content .hero-sub-title{
  color: var(--theme-color);
}

.hs-2 .hero-single .hero-content .hero-title{
  color: var(--color-dark);
  font-size: 60px;
}

.hs-2 .hero-single .hero-content .hero-title span{
  color: var(--theme-color);
}

.hs-2 .hero-single .hero-content p{
  color: var(--color-dark);
}

.hs-2 .theme-btn{
  background: var(--theme-color);
  color: var(--color-white)
}

.hs-2 .theme-btn2{
  background: var(--color-red);
}

.hs-2 .hero-text-shape p{
  position: absolute;
  font-size: 200px;
  line-height: 200px;
  font-weight: 800;
  color: var(--color-dark);
  opacity: .08;
}

.hs-2 .hero-text-shape .text-1{
  left: 0;
  top: 50px;
}

.hs-2 .hero-text-shape .text-2{
  right: 0;
  bottom: 20px;
}

.hs-2 .hero-slider .owl-dots .owl-dot{
  background-color: var(--theme-color);
}

@media all and (max-width: 991px) {
  .hs-2 .hero-single .hero-img img{
    width: 100%;
  }
}


@media all and (max-width: 767px) {
  .hs-2 .hero-text-shape .text-1{
    font-size: 100px;
  }

  .hs-2 .hero-single{
    padding-top: 180px;
  }
}


/* hs-3 */
.hs-3 .hero-single{
  position: relative;
  padding-top: 150px;
  padding-bottom: 150px;
  z-index: 1;
}

.hs-3 .hero-single-bg{
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.hs-3 .hero-slider .owl-nav{
  position: unset;
}

.hs-3 .hero-slider .owl-nav button{
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.hs-3 .hero-slider .owl-nav .owl-prev{
  left: 20px;
}

.hs-3 .hero-slider .owl-nav .owl-next{
  right: 20px;
}

@media all and (max-width: 1199px){
  .hs-3 .hero-slider .owl-nav{
    display: none;
  }
}




/*====================
16. Small banner css 
======================*/

.small-banner{
  position: relative;
}

.small-banner .banner-item{
	position: relative;
  border-radius: 15px;
}

.small-banner .banner-item img{
	height:100%;
	width:100%;
  border-radius: 15px;
}

.small-banner .banner-content{
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	padding-left: 35px;
}

.small-banner .banner-item h3 {
	font-size: 22px;
	font-weight: 600;
	text-transform: capitalize;
	color: var(--color-dark);
}

.small-banner .banner-item h3 span{
	color: var(--color-red);
}

.small-banner .banner-item p {
  display: inline-block;
  background: var(--color-red);
	font-size: 13px;
	color: var(--color-white);
	font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 2px 20px 2px 14px;
	margin-bottom: 15px;
	border-radius: 50px 0 0 50px;
  clip-path: polygon(100% 0, 90% 50%, 100% 100%, 0 100%, 0 0);
}

.small-banner .banner-item a {
	color: var(--color-dark);
	margin-top: 15px;
	font-size: 14px;
	font-weight: 500;
	display: inline-block;
	text-transform:uppercase;
	border-bottom:2px solid var(--color-dark);
}

.small-banner .banner-item a:hover{
	color: var(--theme-color);
	border-color: var(--theme-color);
}


@media all and (max-width: 1399px) {
  .small-banner .banner-item h3 {
    font-size: 19px;
  }
}

@media all and (max-width: 1199px) {
  .small-banner .banner-content{
    padding-left: 25px;
  }

  .small-banner .banner-item h3 {
    font-size: 17px;
  }
}



/*====================
17. Mid banner css 
======================*/

.mid-banner{
  position: relative;
}

.mid-banner .banner-item{
	position: relative;
  border-radius: 15px;
}

.mid-banner .banner-item img{
	height:100%;
	width:100%;
  border-radius: 15px;
}

.mid-banner .banner-content{
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	padding-left: 50px;
}

.mid-banner .banner-item h3 {
	font-size: 25px;
	font-weight: 700;
  line-height: 1.4;
  letter-spacing: 1px;
	text-transform: uppercase;
	color: var(--color-dark);
}

.mid-banner .banner-item h3 span{
	color: var(--theme-color);
}

.mid-banner .banner-item p {
	font-size: 16px;
	color: var(--color-dark);
	font-weight: 500;
	margin-bottom: 20px;
  letter-spacing: 3px;
	text-transform: uppercase;
  display: inline-block;
  background: var(--theme-color2);
  border-radius: 50px;
  padding: 0 15px;
}

.mid-banner .banner-item .theme-btn{
  border-radius: 8px;
  padding: 8px 20px;
  text-transform: uppercase;
  margin-top: 25px;
  font-size: 14px;
  letter-spacing: 2px;
}


@media all and (max-width: 1199px) {
  .mid-banner .banner-content{
    padding-left: 25px;
  }

  .mid-banner .banner-item h3 {
    font-size: 19px;
  }
} 

@media all and (max-width: 991px) {
  .mid-banner .banner-item p{
    margin-bottom: 10px;
    font-size: 10px;
    padding: 0 9px;
  }

  .mid-banner .banner-item h3{
    font-size: 15px;
  }

  .mid-banner .banner-item .theme-btn{
    margin-top: 10px;
    padding: 5px 15px;
    font-size: 12px;
  }
}



/*====================
18. Big banner css 
======================*/

.big-banner{
  position: relative;
}

.big-banner .banner-wrap{
  position: relative;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  border-radius: 15px;
  padding-top: 50px;
  padding-bottom: 50px;
}

.big-banner .banner-content{
  text-align: center;
}

.big-banner .banner-content h6{
  color: var(--color-dark);
  font-size: 22px;
  text-transform: lowercase;
  letter-spacing: 4px;
}

.big-banner .banner-content h2{
  color: var(--color-dark);
  text-transform: uppercase;
  margin-top: 20px;
  margin-bottom: 20px;
}

.big-banner .banner-content p{
  color: var(--color-dark);
  text-transform: lowercase;
  font-size: 20px;
  border-top: 1px solid var(--color-dark);
  border-bottom: 1px solid var(--color-dark);
  display: inline-block;
}

.big-banner .banner-content .theme-btn{
  border-radius: 50px;
  margin-top: 30px;
  padding: 8px 28px;
}



/*====================
19. Popup banner css 
======================*/

.popup-banner .modal-content{
  border-radius: 12px;
  border: none;
}

.popup-banner .btn-close{
  position: absolute;
  right: 10px;
  top: 10px;
  background: var(--theme-color);
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50px;
  font-size: 19px;
  color: var(--color-white);
  opacity: 1;
  transition: var(--transition);
  z-index: 2;
}

.popup-banner .btn-close:hover{
  background: var(--color-red);
  color: var(--color-white);
}

.popup-banner .modal-body{
  padding: 0px;
}

.popup-banner-content{
  position: relative;
  z-index: 1;
}

.popup-banner-content::before{
  content: "";
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 150px;
  height: 150px;
  border: 20px solid var(--theme-color);
  border-radius: 50%;
  opacity: .1;
  z-index: -1;
}

.popup-banner-content::after{
  content: "";
  position: absolute;
  top: 30px;
  right: 200px;
  width: 100px;
  height: 100px;
  border: 18px solid var(--theme-color);
  border-radius: 50%;
  opacity: .1;
  z-index: -1;
}

.popup-banner-img img{
  width: 100%;
  border-radius: 10px 0 0 10px;
}

.popup-banner-info{
  padding: 20px 35px 20px 10px;
}

.popup-banner-info h6{
  background: var(--color-red);
  color: var(--color-white);
  border-radius: 50px;
  padding: 6px 15px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 2px;
  display: inline-block;
}

.popup-banner-info h2{
  font-size: 30px;
  font-weight: 700;
  margin: 20px 0 12px 0;
  line-height: 1.4;
}

.popup-banner-info h2 span{
  color: var(--theme-color);
}

.popup-banner-info .theme-btn{
  margin-top: 25px;
}


@media all and (max-width: 991px) {
  .popup-banner-img img{
    border-radius: 10px 10px 0 0;
  }

  .popup-banner-info{
    padding: 30px;
  }
}



/*====================
20. Category css 
======================*/

.category-area{
  position: relative;
}

.category-item{
  position: relative;
}

.category-item a{
  width: 100%;
  vertical-align: middle;
}

.category-info{
  text-align: center;
  padding: 15px 15px;
  border: 1px double var(--border-info-color);
  border-radius: 50px;
  transition: var(--transition2);
}

.category-item:hover .category-info{
  border-color: var(--theme-color);
}

.category-info .icon{
  width: 165px;
  height: 165px;
  margin: 0 auto 0 auto;
  border-radius: 50%;
  background: var(--theme-color2);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--box-shadow);
  z-index: 1;
}

.category-info .icon img{
  width: 70px;
}

.category-info:hover .icon img{
  transform: scale(1.1);
}

.category-info .content{
  margin-top: 20px;
}

.category-info .content h4{
  font-size: 18px;
  color: var(--color-dark);
  transition: all .3s ease-in-out;
}

.category-info:hover .content h4{
  color: var(--theme-color);
}

.category-info .content p{
  color: var(--body-text-color);
}

/* category-area2 */
.category-area2 .category-info{
  background: var(--theme-bg-light);
  border: none;
}

.category-area2 .category-slider{
  margin-top: 10px;
}

/* category-area3 */
.category-area3 .category-info{
  border: none;
}

.category-area3 .category-info .icon{
  background: var(--theme-bg-light);
  box-shadow: none;
}

.category-area3 .category-info .icon img{
  width: 120px;
}

@media all and (max-width: 767px) {
  .category-info .icon{
    width: 130px;
    height: 130px;
  }
}




/*====================
21. Shop css 
======================*/

/* shop sidebar */
.shop-sidebar {
  margin-bottom: 30px;
}

.shop-widget {
  background: var(--color-white);
  padding: 25px;
  margin-bottom: 25px;
  border-radius: 10px;
}

.shop-widget-title {
  position: relative;
  font-size: 18px;
  color: var(--color-dark);
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.shop-search-form .form-group {
  position: relative;
}

.shop-search-form .form-control {
  padding: 12px 45px 12px 15px;
  border-radius: 10px;
  box-shadow: none;
}

.shop-search-form .form-control:focus {
  border-color: var(--theme-color);
}

.shop-search-form button {
  position: absolute;
  right: 0;
  top: 0;
  padding: 10px 18px 6px 18px;
  background: transparent;
  border: none;
  color: var(--theme-color);
}

.shop-category-list a{
  width: 100%;
  color: var(--body-text-color);
  margin: 4px 0;
  transition: var(--transition);
}

.shop-category-list a:hover{
  color: var(--theme-color);
  padding-left: 3px;
}

.shop-category-list span{
  float: right;
}

.shop-checkbox-list li{
  margin-bottom: 8px;
}

.shop-checkbox-list.rating i{
  color: var(--theme-color);
}

.shop-checkbox-list .form-check-input {
  box-shadow: none;
  margin-top: 7px;
}

.shop-checkbox-list .form-check-input:checked {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.shop-checkbox-list .form-check-input:focus{
  border-color: var(--theme-color);
}

.shop-checkbox-list .form-check-label {
  color: var(--body-text-color);
  width: 100%;
  margin-left: 4px;
} 

.shop-checkbox-list .form-check-label span{
  float: right;
  margin-right: 3px;
} 

.shop-checkbox-list.color li{
  display: inline-block;
  margin-right: 2px;
}

.shop-checkbox-list.color .form-check{
  position: relative;
  padding-left: 0;
}

.shop-checkbox-list.color .form-check-input {
  position: absolute;
  opacity: 0;
  visibility: hidden;
  z-index: -1;
}

.shop-checkbox-list.color .form-check-label{
  margin-left: 0;
}

.shop-checkbox-list.color .form-check-label span{
  width: 28px;
  height: 28px;
  border-radius: 50px;
  vertical-align: top;
  margin-top: 2.5px;
  margin-right: 5px;
  position: relative;
}

.shop-checkbox-list.color .form-check-label span::before{
  content: "\f00c";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  font-family: "Font Awesome 6 Pro";
  color: var(--color-white);
  text-align: center;
  opacity: 0;
  visibility: hidden;
}

.shop-checkbox-list.color .form-check-input:checked~.form-check-label span::before{
  opacity: 1;
  visibility: visible;
}

/* shop sidebar banner */
.shop-widget-banner{
  position: relative;
  z-index: 1;
}

.shop-widget-banner .banner-img{
  position: absolute;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  z-index: -1;
}

.shop-widget-banner .banner-img::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.6);
  width: 100%;
  height: 100%;
  border-radius: 10px;
  z-index: -1;
}

.shop-widget-banner .banner-content{
  text-align: center;
  padding-top: 100px;
  padding-bottom: 100px;
}

.shop-widget-banner .banner-content h6{
  color: var(--color-white);
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 2px;
  border-top: 2px solid var(--color-white);
  border-bottom: 2px solid var(--color-white);
  display: inline-block;
}

.shop-widget-banner .banner-content h4{
  color: var(--color-white);
  font-size: 28px;
  margin: 15px 0;
}

.shop-widget-banner .banner-content .theme-btn{
  border-radius: 50px;
  padding: 5px 20px;
}


/* shop sort */
.shop-sort{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-white);
  border-radius: 10px;
  padding: 8px 15px;
  margin-bottom: 30px;
}

.shop-sort-box{
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
}

.shop-sort-box .nice-select{
  border-radius: 10px;
  padding-left: 13px;
}

.shop-sort-box .nice-select::after{
  width: 6px;
  height: 6px;
  right: 15px;
}

.shop-sort-box .nice-select .list{
  border: none;
  border-radius: 10px;
  padding-top: 7px;
  padding-bottom: 7px;
  box-shadow: var(--box-shadow);
}

.shop-sort-gl a{
  background: transparent;
  border: 1px solid var(--border-info-color);
  color: var(--body-text-color);
  text-align: center;
  border-radius: 50px;
  width: 35px;
  height: 35px;
  line-height: 35px;
}

.shop-sort-grid{
  margin-right: 5px;
}

.shop-sort-gl .active{
  border-color: var(--theme-color);
  background: var(--theme-color);
  color: var(--color-white);
}


/* shop pagination */
.shop-area .pagination .page-link,
.shop-area2 .pagination .page-link,
.shop-area3 .pagination .page-link{
  border-radius: 50px !important;
}


/* shop area 2 */
.shop-area2 .shop-widget{
  border: 1px solid var(--border-info-color);
  background: transparent;
}

.shop-area2 .shop-sort{
  border: 1px solid var(--border-info-color);
  border-radius: 10px;
  padding: 10px 20px;
}


@media all and (max-width: 767px) {
  .shop-sort{
    flex-direction: column;
    gap: 20px;
  }

  .shop-sort-box{
    flex-direction: column;
    width: 100%;
  }

  .shop-sort-box .nice-select,
  .shop-sort-box .nice-select .list{
    width: 100%;
  }
}



/*===================
22. Shop cart css 
=====================*/

.shop-cart thead tr {
  background: var(--theme-color);
  color: var(--color-white);
}

.shop-cart thead tr th {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: .4px;
}

.shop-cart thead tr th,
.shop-cart thead tr td {
  white-space: nowrap;
}

.shop-cart tr td {
  color: var(--color-dark);
  vertical-align: middle;
  border-bottom: 1px solid var(--border-info-color);
  border-top: none;
  position: relative;
  padding: 20px 10px;
  font-size: 16px;
}

.shop-cart-img {
  width: 100px;
}

.shop-cart-img img {
  width: 100%;
  border-radius: 8px;
  padding: 6px;
  border: 1px solid var(--border-info-color);
}

.shop-cart-name{
  font-size: 16px;
  margin-bottom: 4px;
  font-weight: 600;
}

.shop-cart-name a:hover{
  color: var(--theme-color);
}

.shop-cart-info p{
  font-size: 14px;
  color: var(--body-text-color);
}

.shop-cart-info p span{
  font-weight: 500;
  margin-right: 5px;
}

.shop-cart-price{
  font-weight: 500;
}

.shop-cart-qty {
  width: 110px;
}

.shop-cart-qty button {
  width: 30px;
  height: 30px;
  line-height: 28px;
  color: var(--theme-color);
  text-align: center;
  border: 0;
  border-radius: 50px;
  background: var(--theme-bg-light);
}

.shop-cart-qty button i{
  font-weight: 500;
}

.shop-cart-qty input {
  width: 30px;
  border: none;
  background: transparent;
  color: var(--theme-color);
  text-align: center;
}

.shop-cart-subtotal{
  font-weight: 500;
}

.shop-cart-remove{
  font-size: 18px;
  width: 35px;
  height: 35px;
  line-height: 35px;
  border: 1px solid var(--border-info-color);
  text-align: center;
  border-radius: 50px;
}

.shop-cart-remove:hover {
  border-color: var(--color-red);
  color: var(--color-red);
}

.shop-cart-footer {
  margin-top: 20px;
}

.shop-cart-coupon{
  margin-bottom: 25px;
}

.shop-cart-coupon .form-group{
  position: relative;
}

.shop-cart-coupon .form-control {
  box-shadow: none;
  padding: 15px 160px 15px 25px;
  border-radius: 50px;
}

.shop-cart-coupon .form-control:focus {
  border-color: var(--theme-color);
}

.shop-cart-coupon .theme-btn {
  position: absolute;
  right: 5.4px;
  top: 5.4px;
  border-radius: 50px;
  padding: 8px 20px;
}

.shop-cart-summary {
  padding: 30px;
  border: 1px solid var(--border-info-color);
  background: var(--theme-bg-light);
  border-radius: 10px;
  margin-top: 12px;
  margin-left: 20px;
}

.shop-cart-summary h5{
  margin-bottom: 25px;
}

.shop-cart-summary li {
  margin-bottom: 10px;
}

.shop-cart-summary li span {
  float: right;
}

.shop-cart-summary li strong {
  color: var(--color-dark);
}

.shop-cart-total {
  padding-top: 10px;
  border-top: 1px solid var(--border-info-color);
}

.shop-cart-total span {
  font-weight: bold;
  color: var(--theme-color);
}


@media all and (max-width: 991px) {
  .shop-cart-summary {
    margin-left: 0;
  }
}



/*===================
23. Shop checkout css 
=====================*/

.shop-checkout-step .accordion-item{
  margin-bottom: 20px;
  border: 1px solid var(--border-info-color);
  border-radius: 8px;
}

.shop-checkout-step .accordion-button{
  background: transparent;
  box-shadow: none;
  font-weight: 600;
}

.shop-checkout-step .accordion-button:not(.collapsed){
  background: transparent;
  color: var(--theme-color);
}

.shop-checkout-step .accordion-body{
  border-top: 1px solid var(--border-info-color);
}

.shop-checkout-form .form-group{
  margin-bottom: 15px;
}

.shop-checkout-form label{
  color: var(--color-dark);
  margin-bottom: 4px;
}

.shop-checkout-form .form-control{
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: none;
}

.shop-checkout-form .nice-select{
  width: 100%;
  font-size: 16px;
  border-radius: 8px;
  height: 50px;
  line-height: 47px;
  padding-left: 20px;
  margin-bottom: 15px;
}

.shop-checkout-form .nice-select:after{
  width: 9px;
  height: 9px;
  margin-right: 8px;
  margin-top: -6px;
}

.shop-checkout-form .nice-select .list{
  width: 100%;
  height: 300px;
  overflow-y: auto;
  border-radius: 8px;
}

.shop-checkout-form .form-control:focus,
.shop-checkout-form .nice-select{
  border-color: var(--theme-color);
}

.shop-checkout-form .form-check-input{
  margin-top: 7px;
  box-shadow: none;
}

.shop-checkout-form .form-check-input:focus{
  border-color: var(--theme-color);
}

.shop-checkout-form .form-check-input:checked{
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.shop-checkout-form .theme-btn{
  margin-top: 15px;
  margin-bottom: 10px;
}

.shop-checkout-form .theme-btn2{
  background: var(--theme-color);
  color: var(--color-white);
  margin-right: 8px;
}

.shop-checkout-form .theme-btn2::before{
  background: var(--color-dark);
}

.shop-shipping-method{
  margin-top: 20px;
  padding-top: 25px;
  border-top: 1px solid var(--border-info-color);
}

.shop-shipping-method h6{
  margin-bottom: 20px;
}

.shop-shipping-method .form-check{
  padding-left: 0;
  position: relative;
  margin-bottom: 20px;
}

.shop-shipping-method .form-check-input{
  width: 1.3em;
  height: 1.3em;
  position: absolute;
  top: -2px;
  right: 6px;
}

.shop-shipping-method label{
  font-weight: 500;
  border: 1px solid var(--border-info-color);
  background: var(--color-white);
  padding: 10px 15px 10px 15px;
  border-radius: 10px;
  width: 100%;
}

.shop-shipping-method .form-check-input:checked~label{
  border-color: var(--theme-color) !important;
}

.shop-shipping-method label span{
  display: block;
  color: var(--body-text-color);
  font-weight: 400;
}

.shop-checkout-payment .nav-link{
  background: transparent !important;
  border: 1px solid var(--border-info-color);
  text-align: center;
  border-radius: 12px;
  margin-right: 15px;
  margin-bottom: 20px;
  position: relative;
}

.shop-checkout-payment .nav-link::before{
  content: "\f058";
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  font-weight: bold;
  font-size: 20px;
  color: var(--theme-color);
  transform: scale(0);
  opacity: 0;
  visibility: hidden;
  left: 4px;
  top: -4px;
  transition: var(--transition);
}

.shop-checkout-payment .nav-link.active::before{
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.shop-checkout-payment .nav-link.active{
  border-color: var(--theme-color);
}

.shop-checkout-payment .nav-link span{
  color: var(--body-text-color);
}

.shop-checkout-payment .checkout-card-img{
  padding-top: 12px;
  margin-bottom: 12px;
}

.shop-checkout-payment .checkout-card-img img{
  width: 34px;
}

.shop-checkout-payment .checkout-payment-img img{
  width: 120px;
}

.shop-checkout-payment .checkout-payment-img.cod img{
  width: 52px;
}

.shop-checkout-form.cod .form-check-input{
  width: 1.4em;
  height: 1.4em;
  border-radius: 6px;
}

.shop-checkout-form.cod label{
  font-weight: 500;
  margin-left: 10px;
}

.shop-checkout-form.cod label span{
  display: block;
  font-weight: 400;
  color: var(--body-text-color);
}

.shop-checkout-form.cod label a{
  color: var(--theme-color);
}



/*============================
24. Shop checkout complete css 
==============================*/

.shop-checkout-complete{
  position: relative;
}

.checkout-complete-content{
  background: var(--theme-bg-light);
  border-radius: 20px;
  padding: 50px;
  text-align: center;
  border: 1px solid var(--border-info-color);
}

.checkout-complete-icon{
  width: 60px;
  height: 60px;
  line-height: 60px;
  font-size: 35px;
  color: var(--color-white);
  background: var(--color-green);
  margin: 0 auto;
  border-radius: 50px;
  margin-bottom: 20px;
}

.checkout-complete-content h3{
  text-transform: capitalize;
}

.checkout-complete-content p{
  margin-top: 15px;
  margin-bottom: 30px;
}

@media all and (min-width: 992px) and (max-width: 1399px) {
  .checkout-complete-content{
    padding: 40px;
  }
}




/*===================
25. Shop single css 
=====================*/

.shop-single{
  position: relative;
}

.shop-single-gallery{
  position: relative;
}

.shop-single-video{
  position: absolute;
  width: 45px;
  height: 45px;
  line-height: 42px;
  right: 10px;
  top: 10px;
  padding-left: 5px;
  border: 1px solid var(--border-info-color);
  border-radius: 50px;
  color: var(--body-text-color);
  text-align: center;
  font-size: 20px;
  z-index: 1;
}

.shop-single-video:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.shop-single-gallery .flex-viewport{
  width: 100%;
  border-radius: 10px;
  border: 1px solid var(--border-info-color);
  margin-bottom: 10px;
  text-align: center;
}

.shop-single-gallery .flex-viewport img{
  padding: 30px;
  width: 91%;
}

.shop-single-gallery .flex-control-thumbs li{
  margin: 6.8px;
  padding: 0;
  width: 23%;
} 

.shop-single-gallery .flex-control-thumbs li:first-child{
  margin-left: 0;
}

.shop-single-gallery .flex-control-thumbs li:last-child{
  margin-right: 0;
}

.shop-single-gallery .flex-control-thumbs img{
  border: 1px solid var(--border-info-color);
  border-radius: 10px;
  padding: 10px;
}

.shop-single-gallery .flex-control-thumbs li img.flex-active{
  border-color: var(--theme-color);
}

.shop-single-gallery .flex-direction-nav a{
  border-radius: 50px !important;
  background: var(--theme-color);
  line-height: 40px !important;
  margin-top: -75px;
}

.shop-single-gallery .flex-direction-nav a:hover{
  background: var(--theme-color);
}

.shop-single-info{
  margin-left: 20px;
}

.shop-single-title{
  font-size: 25px;
}

.shop-single-rating{
  margin: 10px 0;
}

.shop-single-rating i{
  color: var(--theme-color);
}

.shop-single-rating .rating-count{
  margin-left: 10px;
  display: inline-block;
}

.shop-single-price{
  margin-top: 5px;
  margin-bottom: 5px;
  font-size: 23px;
  font-weight: 500;
}

.shop-single-price del{
  margin-right: 5px;
  font-size: 18px;
  color: var(--body-text-color);
}

.shop-single-price .amount {
  color: var(--theme-color);
}

.shop-single-price .discount-percentage{
  color: var(--color-red);
  font-size: 15px;
}

.shop-single-cs{
  border-top: 1px solid var(--border-info-color);
  padding-top: 20px;
}

.shop-single-cs h6{
  margin-bottom: 15px;
  color: var(--body-text-color);
  font-weight: 500;
}

.shop-single-size .nice-select{
  border-radius: 8px;
  padding-left: 15px;
}

.shop-single-size .nice-select::after{
  width: 6px;
  height: 6px;
}

.shop-single-cs .shop-cart-qty button{
  width: 35px;
  height: 35px;
  line-height: 34px;
  transition: var(--transition);
}

.shop-single-cs .shop-cart-qty button:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.shop-single-sortinfo li{
  font-weight: 500;
  margin: 10px 0;
}

.shop-single-sortinfo a,
.shop-single-sortinfo span{
  font-weight: 400;
  margin-left: 5px;
}

.shop-single-sortinfo a{
  color: var(--body-text-color);
}

.shop-single-sortinfo a:hover{
  color:var(--theme-color)
}

.shop-single-action{
  border-top: 1px solid var(--border-info-color);
  padding-top: 20px;
  margin-top: 20px;
}

.shop-single-action .theme-btn{
  padding: 8px 20px;
}

.shop-single-action .theme-btn2{
  margin-left: 10px;
  border: 1px solid var(--border-info-color);
  box-shadow: none;
  text-align: center;
  padding: 8px 15px;
}

.shop-single-action .theme-btn2 span{
  margin-right: 0;
}

.shop-single-share a{
  width: 40px;
  height: 40px;
  line-height: 39px;
  border: 1px solid var(--border-info-color);
  border-radius: 50px;
  text-align: center;
  color: var(--body-text-color);
  margin-left: 5px;
}

.shop-single-share a:hover{
  background: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--color-white);
}

.shop-single-details{
  margin-top: 50px;
}

.shop-single-desc{
  padding-top: 30px;
}

.shop-single-additional{
  padding-top: 30px;
}

.shop-single-review{
  padding-top: 30px;
}

.shop-single-details .nav-tabs {
  border-width: 2px;
}

.shop-single-details .nav-tabs .nav-link {
  color: var(--color-dark);
  font-size: 18px;
  padding: 12px 25px;
  font-weight: 500;
  border: none;
  background: transparent;
  border-bottom: 2px solid transparent;
}

.shop-single-details .nav-tabs .nav-link:hover,
.shop-single-details .nav-tabs .nav-link.active {
  border-bottom: 2px solid var(--theme-color);
  color: var(--theme-color);
}

.shop-single-list{
  margin: 20px 0;
}

.shop-single-list .title{
  margin-bottom: 15px;
  color: var(--color-dark);
  font-size: 17px;
}

.shop-single-list ul li{
  margin: 5px 0;
  list-style: unset;
  margin-left: 15px;
}

.shop-single-list ul li span{
  color: var(--color-dark);
  margin-right: 5px;
}

.shop-single-review .blog-comments-item{
  position: relative;
  border: 1px solid var(--border-info-color);
  border-radius: 10px;
  padding: 30px;
}

.shop-single-review .review-rating{
  position: absolute;
  right: 30px;
  top: 30px;
}

.shop-single-review .review-rating i {
  color: var(--theme-color);
}

.shop-single-review .blog-comments-content h5{
  font-size: 17px;
  margin-bottom: 5px;
}

.shop-single-review .blog-comments-content i{
  margin-right: 5px;
}


@media all and (max-width: 1399px) {
  .shop-single-gallery .flex-control-thumbs li{
    margin: 6px;
  } 

  .shop-single-action .theme-btn2{
    margin-left: 2px;
  }
}

@media all and (max-width: 1199px) {
  .shop-single-color{
    margin-top: 20px;
  }

  .shop-single-action .shop-single-btn{
    margin-bottom: 20px;
  }
}

@media all and (max-width: 991px) {
  .shop-single-info{
    margin-left: 0;
    margin-top: 30px;
  }

  .shop-single-color{
    margin-top: 0px;
  }

  .shop-single-action .shop-single-btn{
    margin-bottom: 0px;
  }
}

@media all and (max-width: 767px) {
  .shop-single-gallery .flex-control-thumbs li{
    margin: 4.6px;
  } 

  .shop-single-size{
    margin-top: 20px;
  }

  .shop-single-color{
    margin-top: 20px;
  }

  .shop-single-action .shop-single-btn{
    margin-bottom: 20px;
  }

  .shop-single-details .nav-tabs .nav-link{
    font-size: 16px;
    padding: 10px 10px;
  }

  .shop-single-review .review-rating{
    right: 10px;
    top: 5px;
  }
}



/*====================
26. Compare css 
======================*/

.compare-table {
  background: var(--theme-bg-light);
  white-space: nowrap;
}

.compare-table th{
  vertical-align: middle;
}

.compare-table tbody tr th,
.compare-table tbody tr td {
  padding: 10px 15px;
}

.compare-img{
  position: relative;
}

.compare-img img{
  border-radius: 10px;
}

.compare-remove{
  position: absolute;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  right: 10px;
  top: 10px;
  background: var(--theme-color);
  color: var(--color-white);
  border-radius: 50px;
}

.compare-remove:hover{
  background: var(--color-red);
  color: var(--color-white);
}

.compare-title{
  font-size: 17px;
  font-weight: 500;
}

.compare-title:hover{
  color: var(--theme-color);
}

.compare-price{
  color: var(--theme-color);
  font-weight: 600;
}

.compare-rate i{
  color: var(--color-yellow);
}

.compare-table .theme-btn{
  padding: 8px 15px;
}



/*===================
27. Price range css 
=====================*/

.price-range-box{
  margin-bottom: 12px;
}

.price-range.ui-widget-content {
  border: none;
  background: var(--border-info-color);
  height: 5px;
  border-radius: 3px;
}

.price-range .ui-state-default,
.price-range .ui-widget-content .ui-state-default {
  top: -7px;
  width: 18px;
  height: 18px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  margin-left: 0px;
  background: var(--theme-color);
  z-index: 1;
}

.price-range .ui-state-focus {
  outline: none;
}

.price-range .ui-widget-header {
  background: var(--theme-color);
  border-radius: 0 !important;
}

.price-range-input input {
  border: none;
  margin-bottom: 20px;
  color: var(--theme-color);
  background: transparent;
  font-weight: 700;
  outline: none;
}



/*====================
28. Product css 
======================*/

.product-area{
  position: relative;
}

.product-item{
  position: relative;
  padding: 20px;
  border-radius: 10px;
  z-index: 1;
}

.product-item::before{
  content: "";
  position: absolute;
  height: 70%;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--theme-bg-light);
  border-radius: 20px;
  transition: var(--transition2);
  z-index: -1;
}

.product-item:hover::before{
  height: 100%;
}

.product-img{
  position: relative;
}

.product-item .type{
  position: absolute;
  right: 0px;
  top: 0px;
  display: inline-block;
  background: var(--theme-color);
  color: var(--color-white);
  font-size: 11px;
  padding: 2px 15px;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
  border-radius: 50px;
}

.product-item .type.new{
  background: var(--theme-color);
}

.product-item .type.oos{
  background: var(--color-red);
}

.product-item .type.discount{
  background: var(--color-yellow);
}

.product-item .type.hot{
  background: var(--color-skyblue);
}

.product-action-wrap{
  position: absolute;
  bottom: 0px;
  width: 100%;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.product-item:hover .product-action-wrap{
  opacity: 1;
  visibility: visible;
  bottom: 10px;
}

.product-action a{
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: var(--theme-color);
  color: var(--color-white);
  text-align: center;
  border-radius: 50%;
  margin: 0 3px;
}

.product-action a:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.product-content{
  margin-top: 2px;
}

.product-title a{
  font-size: 17px;
  color: var(--color-dark);
}

.product-title a:hover{
  color: var(--theme-color);
}

.product-rate{
  color: var(--color-yellow);
  font-size: 15px;
  margin-top: 3px;
}

.product-bottom{
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.product-price{
  font-size: 16px;
  font-weight: 500;
}

.product-price del{
  margin-right: 5px;
  color: var(--body-text-color);
}

.product-price span{
  color: var(--color-red);
}

.product-cart-btn{
  position: relative;
  border: none;
  background: var(--theme-color);
  color: var(--color-white);
  border-radius: 50%;
  text-align: center;
  font-size: 18px;
  width: 42px;
  height: 42px;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.product-cart-btn:hover{
  color: var(--color-white);
  background: var(--theme-color);
}

@media all and (min-width: 992px) and (max-width: 1399px) {
  .product-item{
    padding: 15px;
  }

  .product-cart-btn{
    width: 37px;
    height: 37px;
    font-size: 15px;
  }

  .product-price del{
    font-size: 13px;
  }
}

@media all and (max-width: 767px){
  .product-wrap{
    margin: 0 15px;
  }
}


/* product slider */
.product-slider .owl-nav button{
  position: absolute;
	background: var(--theme-color)!important;
  color: var(--color-white)!important;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 20px!important;
  text-align: center;
	border-radius: 50%;
	box-shadow: var(--box-shadow);
  transition: var(--transition);
  top: 50%;
  transform: translateY(-50%);
}

.product-slider .owl-nav button:hover{
	background: var(--theme-color)!important;
}

.product-slider .owl-prev{
	left: -16px;
}

.product-slider .owl-next{
	right: -16px;
}

/* product slider 2 */
.product-slider2 .product-item{
  margin-bottom: 0px;
}

.product-slider2 .owl-nav button{
  position: absolute;
	background: var(--theme-color)!important;
  color: var(--color-white)!important;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 20px!important;
  text-align: center;
	border-radius: 50%;
	box-shadow: var(--box-shadow);
  transition: var(--transition);
  top: 50%;
  transform: translateY(-50%);
}

.product-slider2 .owl-nav button:hover{
	background: var(--theme-color)!important;
}

.product-slider2 .owl-prev{
	left: -16px;
}

.product-slider2 .owl-next{
	right: -16px;
}

/* item-big */
.item-big .product-img{
  text-align: center;
  width: 100%;
}

.item-big .product-img img{
 width: 80%;
 margin: auto;
}

/* item-2 */
.item-2 .product-item{
  background: var(--theme-bg-light);
  border-radius: 20px;
}

.item-2 .product-item::before{
  display: none;
}

/* item-3 */
.item-3 .product-item::before{
  background: var(--color-white) !important;
  border-radius: 20px;
}

.item-3 .product-img{
  margin-bottom: 15px;
}

/* item list*/
.item-list .product-item{
  display: flex;
  align-items: center;
  gap: 30px;
  background: var(--color-white);
  border-radius: 20px;
}

.item-list .product-item::before{
  display: none;
}

.item-list.item-2 .product-item{
  background: var(--theme-bg-light);
}

.item-list .product-img{
  width: 220px;
}

.item-list .product-content{
  flex: 1;
}

.item-list .product-content p{
  margin-top: 10px;
  margin-bottom: 15px;
}

/* item tab */
.item-tab .nav-link{
  font-size: 17px;
  font-weight: 500;
  color: var(--color-dark);
  padding: 0px;
  margin-right: 20px;
  border-radius: 0;
  border-bottom: 2px solid transparent;
}

.item-tab .nav-item:last-child .nav-link{
  margin-right: 0;
}

.item-tab .nav-link.active{
  color: var(--theme-color);
  background: transparent;
  border-bottom-color: var(--theme-color);
}

@media all and (max-width: 767px) {
  .item-list .product-item{
    flex-direction: column;
    align-items: unset;
    gap: 5px;
  }

  .item-list .product-img{
    width: 100%;
  }

  .item-tab{
    flex-direction: column;
    align-items: start;
    gap: 20px;
  }
}




/*====================
29. Product list css 
======================*/

.product-list{
  position: relative;
}

.product-list-title{
  position: relative;
  font-size: 25px;
  margin-bottom: 40px;
  padding-bottom: 15px;
  font-weight: 700;
  border-bottom: 1px solid var(--border-info-color);
}

.product-list-title::before{
  content: "";
  position: absolute;
  left: 0;
  bottom: -1.8px;
  width: 40px;
  height: 2px;
  background: var(--theme-color);
}

.product-list-item{
  display: flex;
  gap: 14px;
  background: var(--theme-bg-light);
  align-items: center;
  border-radius: 15px;
  margin: 25px 0;
  padding: 10px;
  position: relative;
  transition: var(--transition);
}

.product-list-item:last-child{
  margin-bottom: 0;  
}

.product-list-img{
  background: var(--color-white);
  border: 1px solid var(--border-info-color);
  border-radius: 15px;
  padding: 10px;
}

.product-list-img img{
  width: 100px;
  height: 100px;
}

.product-list-item:hover .product-list-img img{
  transform: scale(1.1);
}

.product-list-content{
  flex: 1;
}

.product-list-content h4 a{
  font-size: 18px;
}

.product-list-content h4 a:hover{
  color: var(--theme-color);
}

.product-list-rate{
  font-size: 15px;
  color: var(--color-yellow);
  margin-top: 4px;
}

.product-list-price{
  margin-top: 4px;
  font-weight: 500;
}

.product-list-price del{
  margin-right: 10px;
}

.product-list-price span{
  color: var(--color-red);
}

.product-list-btn{
  position: absolute;
  right: -3px;
  bottom: -3px;
  width: 50px;
  height: 50px;
  line-height: 36px;
  border-radius: 50%;
  border: 7px solid var(--color-white);
  background: var(--theme-color);
  color: var(--color-white);
  text-align: center;
}

.product-list-btn:hover{
  background: var(--theme-color);
  color: var(--color-white);
}


@media all and (max-width: 1399px) {
  .product-list-content h4 a{
    font-size: 16px;
  }
}


/*======================
30. Modal quick view css 
========================*/

.quickview .modal-content{
  border-radius: 15px;
}

.quickview .modal-body{
  padding: 30px;
}

.quickview .btn-close{
  position: absolute;
  right: 10px;
  top: 10px;
  background: var(--theme-color);
  color: var(--color-white);
  font-size: 18px;
  width: 25px;
  height: 25px;
  line-height: 27px;
  border-radius: 50px;
  opacity: 1;
  transition: var(--transition);
  z-index: 1;
}

.quickview .btn-close:hover{
  background: var(--color-red);
  color: white;
}

.quickview-title{
  margin-bottom: 5px;
}

.quickview-rating i{
  color: var(--color-yellow);
}

.quickview-price {
  margin-top: 15px;
  margin-bottom: 15px;
}

.quickview-price h5{
  font-weight: 500;
}

.quickview-price del {
  margin-right: 10px;
  color: var(--body-text-color);
}

.quickview-price span {
  color: var(--theme-color);
}

.quickview-list li{
  margin: 5px 0;
}

.quickview-list li span{
  font-weight: 500;
  margin-left: 10px;
}

.quickview-list li .stock{
  color: var(--theme-color)
}

.quickview-cart{
  margin: 15px 0;
}

.quickview-cart .theme-btn{
  padding: 5px 18px;
}

.quickview-social a{
  width: 30px;
  height: 30;
  line-height: 32px;
  text-align: center;
  color: var(--color-dark2);
}

.quickview-social a:hover{
  color: var(--theme-color);
}


/*====================
31. Bs custom css 
======================*/

/* tooltip */
.tooltip-inner {
  background: var(--theme-color);
  border-radius: 50px;
  padding: 2px 10px;
  font-size: 12px;
  font-weight: 600;
}

.bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before{
  border-top-color: var(--theme-color) !important;
}

.bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before{
  border-top-color: var(--theme-color) !important;
}

.bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before{
  left: -2.5px !important;
  border-left-color: var(--theme-color) !important;
}

.bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before{
  right: -2.5px !important;
  border-right-color: var(--theme-color) !important;
}

.tooltip.show {
  opacity: 1;
}

/* modal */
.modal-open{
  overflow: auto;
  padding-right: 0 !important;
}




/*=============================
32. Countdown css
===============================*/

.countdown{
  position: relative;
  margin: 15px 0;
}

.countdown-item{
  background: var(--color-white);
  text-align: center;
  margin: 5px;
  padding: 12px 20px 16px 20px;
  border-radius: 50px;
  border: 1px solid var(--border-info-color);
}

.countdown-item h2{
  color: var(--color-red);
  font-weight: 700;
}

.countdown-item h5{
  margin-top: 2px;
  color: var(--color-dark);
}




/*====================
33. Deal css 
======================*/

.deal-area{
  position: relative;
  background: var(--theme-color2);
  overflow: hidden;
  z-index: 1;
}

.deal-area::before{
  content: "";
  position: absolute;
  right: 5%;
  bottom: 0;
  width: 570px;
  height: 570px;
  border-radius: 50% 50% 0 50%;
  background: var(--color-yellow);
  opacity: .5;
  transform: rotate(80deg);
  z-index: -1;
}

.deal-text-shape{
  position: absolute;
  left: 0;
  top: 0;
  writing-mode: vertical-lr;
  transform: scale(-1, -1);
  letter-spacing: 15px;
  font-size: 180px;
  line-height: 180px;
  font-weight: 800;
  text-transform: uppercase;
  color: var(--color-yellow);
  opacity: .3;
  z-index: -1;
}

.deal-content{
  margin-right: 80px;
}

.deal-info span{
  color: var(--color-red);
  font-size: 20px;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 5px;
}

.deal-info h1{
  font-weight: 700;
  margin-top: 5px;
  margin-bottom: 15px;
}

.deal-info p{
  color: var(--color-dark);
  font-size: 17px;
}

.deal-img{
  position: relative;
}

.deal-discount{
  position: absolute;
  width: 120px;
  height: 120px;
  top: 0;
  left: 20%;
  background: var(--color-red);
  color: var(--color-white);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.deal-discount span{
  font-size: 40px;
  line-height: 40px;
  font-weight: 800;
}

.deal-discount span:last-child{
  font-size: 25px;
  line-height: 25px;
  font-weight: 600;
  text-transform: uppercase;
}

@media all and (max-width: 991px) {
  .deal-img{
    margin-top: 50px;
  }
}

@media all and (max-width: 767px) {
  .deal-content{
    margin-right: 0;
  }
}


/* deal-countdown */
.deal-countdown{
  padding: 0 7px;
  margin-top: 25px;
  margin-bottom: 35px;
}

.deal-countdown .row{
  gap: 8px;
}

.deal-countdown .countdown-item{
  margin: 3px;
  padding: 10px 6px;
  border-radius: 20px;
}

.deal-countdown .countdown-item h2{
  font-size: 50px;
  margin-bottom: 4px;
}

.deal-countdown .countdown-item h5{
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: .5px;
}

/* deal-slider */
.deal-slider .owl-dots{
  text-align: center;
  margin-top: 30px;
}

.deal-slider .owl-dots button{
  width: 12px;
  height: 12px;
  border-radius: 50px;
  border: 3px solid var(--theme-color);
  margin: 4px;
  transition: var(--transition2);
}

.deal-slider .owl-dots button.active{
  background: var(--theme-color);
}



/*====================
34. Feature css 
======================*/

.feature-area{
  position: relative;
}

.feature-wrap{
  padding: 40px 20px;
  border: 1px solid var(--border-info-color);
  border-radius: 15px;
}

.feature-wrap .feature-item{
  border-right: 1px solid var(--border-info-color);
}

.feature-wrap [class*=col-]:last-child .feature-item{
  border-right: none;
}

.feature-item{
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.feature-icon{
  width: 65px;
  height: 65px;
  line-height: 65px;
  background: var(--theme-color);
  color: var(--color-white);
  font-size: 30px;
  border-radius: 50%;
  text-align: center;
}

.feature-content h4{
  font-size: 20px;
  margin-bottom: 5px;
  font-weight: 600;
}

/* feature-area2 */
.feature-area2 .feature-wrap{
  background: var(--theme-color2);
  border: none;
}

.feature-area2 .feature-content p{
  color: var(--theme-color);
  font-weight: 500;
}


@media all and (min-width: 1199px) and (max-width: 1399px){
  .feature-wrap{
    padding: 40px 10px;
  }

  .feature-item{
    gap: 11px;
  }
}

@media all and (max-width: 1199px) {
  .feature-content{
    flex: 1;
  }
  
  .feature-wrap .feature-item{
    border-right: none;
  }
}

@media all and (max-width: 991px) {
  .feature-item {
    margin: 10px 0;
  }
}

@media all and (max-width: 767px) {
  .feature-item {
    flex-direction: column;
    text-align: center;
  }
}



/*====================
35. About css 
======================*/

.about-left{
  position: relative;
}

.about-shape{
  position: absolute;
  left: -50px;
  top: -50px;
  width: 150px;
  z-index: -1;
}

.about-img{
  position: relative;
}

.about-img .img-1{
  width: 65%;
}

.about-img .img-2{
  position: absolute;
  right: 0;
  top: -45px;
  width: 200px;
}

.about-img .img-3{
  position: absolute;
  right: 0;
  bottom: -45px;
  width: 42%;
}

.about-img img{
  border-radius: 15px;
}

.about-experience{
  position: absolute;
  display: flex;
  align-items: center;
  gap: 10px;
  top: 20px;
  left: 20px;
  background:  var(--color-white);
  border-radius: 15px;
  padding: 10px 20px 10px 10px;
  color: var(--color-dark);
  box-shadow: var(--box-shadow2);
}

.about-experience-icon{
  color: var(--color-white);
  font-size: 45px;
  width: 70px;
  height: 70px;
  line-height: 60px;
  text-align: center;
  background: var(--theme-color);
  border-radius: 50px;
}

.about-experience-icon img{
  width: 55px;
  filter: brightness(0) invert(1);
}

.about-right{
  position: relative;
  display: block;
  padding-left: 30px;
}

.about-list{
  margin-top: 20px;
  margin-bottom: 10px;
}

.about-list ul{
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.about-list ul li{
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: var(--color-dark);
}

.about-list ul li i{
  width: 35px;
  height: 35px;
  line-height: 35px;
  color: var(--color-dark);
  text-align: center;
  background: var(--theme-color2);
  border-radius: 50px;
}


@media all and (max-width: 991px) {
  .about-right{
    margin-top: 30px;
    padding-left: 0px;
  }
}



/*====================
36. Counter css 
======================*/

.counter-area {
  position: relative;
  background: var(--theme-color);
  z-index: 1;
}

.counter-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  position: relative;
  z-index: 1;
}

.counter-box .icon {
  position: relative;
  text-align: center;
  font-size: 60px;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  line-height: 83px;
  color: var(--color-white);
  border: 3px solid var(--color-white);
  background:  var(--color-dark);
  z-index: 1;
}

.counter-box .icon img{
  filter:brightness(0) invert(1);
  width: 55px;
}

.counter-box .counter {
  display: block;
  line-height: 1;
  color: var(--color-white);
  font-size: 50px;
  font-weight: 600;
}

.counter-box .counter-amount{
  display: flex;
  color: var(--color-white);
}

.counter-box .counter-amount .counter-sign{
  margin-left: 5px;
  font-size: 20px;
  font-weight: 500;
}

.counter-box .title {
  color: var(--color-white);
  margin-top: 10px;
  font-size: 20px;
  font-weight: 600;
  text-transform: capitalize;
}

@media all and (max-width: 991px) {
  .counter-area .counter-box {
    margin: 40px 0;
  }
}



/*===================
37. Team css 
=====================*/

.team-area {
  position: relative;
  overflow: hidden;
}

.team-item {
  padding: 15px;
  margin-bottom: 60px;
  text-align: center;
  position: relative;
  background: var(--color-white);
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.team-item:hover {
  transform: translateY(-10px);
}

.team-img img{
  border-radius: 15px;
  margin-top: -50px;
}

.team-content {
  padding: 10px 0 5px 0;
}

.team-bio h5 {
  font-size: 20px;
  margin-top: 5px;
  margin-bottom: 5px;
  text-transform: capitalize;
}

.team-bio span {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-color);
  text-transform: capitalize;
}

.team-bio h5 a:hover {
  color:  var(--theme-color);
}

.team-social {
  border-top: 1px solid var(--border-info-color);
  margin-top: 10px;
  padding-top: 10px;
}

.team-social a {
  display: inline-block;
  color: var(--color-white);
  width: 40px;
  height: 40px;
  line-height: 40px;
  margin: 4px;
  border-radius: 50px;
  background: var(--theme-color);
  box-shadow: var(--box-shadow);
}

.team-social a:hover {
  background: var(--theme-color2);
  color: var(--color-dark);
}



/*===================
38. Play btn
=====================*/

.play-btn {
  display: inline-block;
  padding: 0;
  height: 75px;
  width: 75px;
  line-height: 75px;
  font-size: 20px;
  text-align: center;
  background: var(--theme-color2);
  color: var(--color-dark) !important;
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.play-btn i::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  background-color: var(--theme-color2);
  border-radius: 50px;
  animation: ripple-wave 1s linear infinite;
  -webkit-transform: scale(1);
  transform: scale(1);
  transition: all 0.5s ease-in-out;
}

@keyframes ripple-wave {
  0% {
    opacity: 0.8;
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }

  100% {
    opacity: 0;
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
  }
}



/*====================
39. Video css 
======================*/

.video-content {
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 15px;
}

.video-content::before {
  content: "";
  position: absolute;
  background: rgba(0, 0, 0, .01);
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  border-radius: 15px;
}

.video-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  border-radius: 5px;
  height: 500px;
  z-index: 1;
}

.video-wrapper img {
  border-radius: 12px;
}

.video-area .play-btn {
  display: inline-block;
  padding: 0;
  height: 75px;
  width: 75px;
  text-align: center;
  position: absolute;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@media all and (max-width: 767px) {
  .video-wrapper {
    height: 250px;
  }
}



 
/*====================
40. Testimonial css 
======================*/

.testimonial-area {
  position: relative;
  background: var(--theme-bg-light);
}

.ts-bg{
  position: relative;
}

.ts-bg::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/02.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  opacity: .15;
}

.testimonial-item {
  position: relative;
  margin-bottom: 20px;
  background: var(--color-white);
  border-radius: 15px;
  padding: 30px;
}

.testimonial-author{
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.testimonial-author-img{
  width: 70px;
}

.testimonial-author-img img{
  border-radius: 50%;
}

.testimonial-author-info h4{
  font-size: 19px;
  color:  var(--color-dark);
}

.testimonial-author-info p{
  color: var(--theme-color);
  font-weight: 500;
}

.testimonial-quote-icon{
  position: absolute;
  right: 30px;
  bottom: 20px;
  width: 100px;
  opacity: .2;
}

.testimonial-rate{
  color: var(--theme-color);
  margin-top: 15px;
}

.testimonial-slider .owl-dots{
  text-align: center;
  margin-top: 20px;
}

.testimonial-slider .owl-dots .owl-dot span{
  background: var(--theme-color);
  margin: 5px;
  border-radius: 50px;
  width: 8px;
  height: 8px;
  display: inline-block;
  transition: var(--transition);
}

.testimonial-slider .owl-dots .owl-dot.active span{
  background-color: var(--theme-color);
  width: 20px;
}




/*====================
41. Brand css 
======================*/

.brand-area{
  position: relative;
}

.brand-area .site-title{
  font-size: 22px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.brand-area .site-title span{
  color: var(--color-yellow);
}

.brand-slider{
  margin-top: 50px;
  margin-bottom: 50px;
}

/* brand-area2 */
.brand-area2 .brand-item{
  padding: 15px 10px;
  border: 1px solid var(--border-info-color);
  border-radius: 10px;
}



/*====================
42. Help css 
======================*/

.help-area{
  position: relative;
}

.help-search{
  text-align: center;
  margin-bottom: 60px;
}

.help-search h3{
  margin-bottom: 5px;
}

.help-search-form{
  margin-top: 20px;
}

.help-search-form .form-group{
  position: relative;
}

.help-search-form .form-control{
  padding: 12px 50px 12px 20px;
  border-radius: 8px;
  box-shadow: none;
}

.help-search-form .form-control:focus{
  border-color: var(--theme-color);
}

.help-search-form .form-group button{
  position: absolute;
  top: 0px;
  right: 0px;
  padding: 10px 15px 6px 15px;
  background: transparent;
  border: none;
  color: var(--body-text-color);
}

.help-search-keyword{
  text-align: left;
  margin-top: 5px;
}

.help-search-keyword span{
  color: var(--color-dark);
}

.help-search-keyword a{
  color: var(--body-text-color);
}

.help-search-keyword a:hover{
  color: var(--theme-color);
}

.help-item{
  text-align: center;
  margin-bottom: 25px;
  border: 1px solid var(--border-info-color);
  border-radius: 10px;
  padding: 30px;
}

.help-icon i{
  font-size: 45px;
  font-weight: 300;
  color: var(--theme-color);
}

.help-content h4{
  margin-top: 15px;
  margin-bottom: 10px;
  font-size: 20px;
} 

.help-item .theme-btn{
  border-radius: 50px;
  padding: 6px 20px;
  margin-top: 25px;
}

.help-bottom{
  text-align: center;
  margin-top: 30px;
}

.help-bottom p{
  margin-top: 5px;
  margin-bottom: 20px;
}



/*====================
43. Invoices css 
======================*/

.invoice-area{
  position: relative;
}

.invoice-container.not-print{
  padding: 0 150px;
}

.invoice-print{
  text-align: right;
  margin-bottom: 30px;
}

.invoice-wrapper{
  background: var(--color-white);
  border-radius: 10px;
  padding: 50px;
}

.invoice-header{
  display: flex;
  justify-content: space-between;
}

.invoice-width{
  width: 40%;
}

.invoice-logo img{
  width: 180px;
}

.invoice-date-box{
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
}

.invoice-address-box{
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.invoice-address h5{
  margin-bottom: 8px;
}

.invoice-table{
  margin-top: 30px;
}

.invoice-table table{
  width: 100%;
  border-collapse: collapse;
}

.invoice-table table,
.invoice-table td, 
.invoice-table th {
  border: 1px solid var(--border-info-color);
}

.invoice-table td, 
.invoice-table th {
  padding: 10px 20px;
}

.invoice-bottom{
  margin-top: 30px;
  padding-top: 15px;
  border-top: 1px solid var(--border-info-color);
  display: flex;
  justify-content: space-between;
}

.invoice-bottom a{
  color: var(--body-text-color);
}



/*====================
44. Faq css 
======================*/

.faq-area{
  position: relative;
}

.faq-area .list-group-item{
  color: var(--body-text-color);
  font-weight: 500;
  font-size: 16px;
  padding: 10px 20px;
}

.faq-area .list-group-item.active{
  background: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--color-white);
  font-size: 18px;
  z-index: 1;
}

.faq-area .list-group-item:first-child{
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.faq-area .list-group-item:last-child {
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
}

.faq-area .accordion-item {
  border: 1px solid var(--border-info-color);
  margin-bottom: 15px;
  background: var(--color-white);
  border-radius: 12px !important;
}

.faq-area .accordion-item span {
  width: 38px;
  height: 38px;
  margin-right: 15px;
}

.faq-area .accordion-item i {
  width: 38px;
  height: 38px;
  line-height: 38px;
  border-radius: 12px;
  background: var(--theme-color);
  text-align: center;
  color: var(--color-white);
}

.faq-area .accordion-button:not(.collapsed) {
  color: var(--theme-color);
  background: transparent;
  box-shadow: inset 0 -1px 0 rgb(0 0 0 / 13%);
}

.faq-area .accordion-button {
  border-radius: 0px !important;
  background: transparent;
  font-weight: 600;
  font-size: 16px;
  padding: 8px 20px 8px 8px;
  color:  var(--body-text-color);
  text-transform: capitalize;
  box-shadow: none !important;
}

.faq-area .accordion-body{
  border-top: 1px solid var(--border-info-color);
  color: var(--body-text-color);
}


@media all and (max-width: 991px) {
  .faq-area .accordion-button {
    font-size: 16px;
  }
}



/*====================
45. Auth css 
======================*/

.login-form {
  padding: 40px;
  background: var(--color-white);
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.login-form .login-header {
  text-align: center;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-info-color);
}

.login-form .login-header img {
  width: 200px;
  margin-bottom: 10px;
}

.login-form .login-header h3 {
  color: var(--theme-color);
  margin-bottom: 5px;
  font-weight: 800;
}

.login-form .login-header p {
  font-size: 20px;
}

.login-form .form-group {
  margin-bottom: 20px;
}

.login-form label {
  color:  var(--color-dark);
  margin-bottom: 5px;
}

.login-form .form-group .form-control {
  border: 1px solid #e8e8e8;
  border-radius: 10px;
  padding: 12px 18px;
  box-shadow: none;
  transition: .5s;
}

.login-form .form-group .form-control:focus {
  border-color: var(--theme-color);
}

.login-form .form-check-input {
  box-shadow: none;
}

.login-form .form-check-input:focus{
  border-color: var(--theme-color);
}

.login-form .forgot-pass {
  color: var(--theme-color);
  transition: .5s;
}

.login-form .forgot-pass:hover {
  color:  var(--theme-color);
}

.login-form .theme-btn {
  width: 100%;
}

.login-form .theme-btn::before{
  width: 460px;
  height: 460px;
}

.login-form .theme-btn i {
  margin-right: 5px;
}

.login-form .form-check-input {
  margin-top: 6.3px;
}

.login-form .form-check-label a {
  color: var(--theme-color);
  transition: .5s;
}

.login-form .form-check-label a:hover {
  color: var(--theme-color);
}

.form-check-input:checked {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.login-form .login-footer {
  margin-top: 25px;
}

.login-form .login-footer p {
  text-align: center;
}

.login-form .login-footer p a {
  color: var(--theme-color);
  transition: .5s;
}

.login-form .login-footer a:hover {
  color:  var(--theme-color);
}

.social-login {
  position: relative;
  border-top: 1px solid #F1F1F1;
  margin-top: 25px;
  padding-top: 15px;
}

.social-divider{
  position: absolute;
  left: 50%;
  top: -21px;
  padding: 5px;
  background: var(--color-white);
  transform: translateX(-50%);
}

.social-login p {
  margin: 10px 0;
}

.social-login-list {
  text-align: center;
}

.social-login-list a {
  padding: 5px 20px;
  border: 2px solid var(--border-info-color);
  border-radius: 50px;
  margin: 5px;
}

.social-login-list a i{
  margin-right: 5px;
}

.social-login-list .fb-auth{
  border-color: #3B5998;
  color: #3B5998;
}

.social-login-list .fb-auth:hover{
  background: #3B5998;
  color: var(--color-white)!important;
}

.social-login-list .gl-auth{
  border-color: #DD4B39;
  color: #DD4B39;
}

.social-login-list .gl-auth:hover{
  background: #DD4B39;
  color: var(--color-white)!important;
}

.social-login-list .tw-auth{
  border-color: #55ACEE;
  color: #55ACEE;
}

.social-login-list .tw-auth:hover{
  background: #55ACEE;
  color: var(--color-white)!important;
}


@media all and (min-width: 992px) and (max-width: 1399px) {
  .login-form{
    padding: 30px;
  }

  .social-login-list a{
    margin-right: 4px;
  }
}

@media only screen and (max-width: 991px) {
  .login-form {
    padding: 40px 20px;
  }
}



/*====================
46. User css 
======================*/

/* nav account */
.nav-account img {
  width: 45px;
  border-radius: 50%;
  padding: 3px;
  border: 3px solid var(--theme-color);
  cursor: pointer;
}

.nav-account .dropdown-menu {
  border: none;
  min-width: 220px;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.nav-account .dropdown-user{
  border-bottom: 1px solid var(--border-info-color);
  padding: 8px 20px;
  margin-bottom: 5px;
}

.nav-account .dropdown-user h5{
  font-size: 15px;
}

.nav-account .dropdown-user p{
  font-size: 14px;
  text-transform: lowercase;
  color: var(--body-text-color);
}

.nav-account .dropdown-menu li {
  width: 100%;
}

.nav-account .dropdown-menu li a i {
  margin-right: 5px;
}

.nav-account .dropdown-menu .dropdown-item {
  padding: 6px 20px;
  font-weight: 500;
}

.nav-account .dropdown-menu .dropdown-item:hover {
  background: transparent;
  color: var(--theme-color);
  padding-left: 25px;
}

/* user sidebar */
.user-area .sidebar {
  background: var(--color-white);
  border-radius: 10px;
  margin-bottom: 25px;
  padding: 15px;
}

.user-area .sidebar-top {
  text-align: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.user-area .sidebar-profile-img {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto 15px auto;
  border-radius: 50px;
  padding: 4px;
  border: 3px solid var(--theme-color);
}

.user-area .sidebar-profile-img img {
  border-radius: 50%;
}

.user-area .sidebar-profile-img button {
  position: absolute;
  border-radius: 50px;
  background: var(--theme-color);
  color: var(--color-white);
  width: 30px;
  height: 30px;
  line-height: 28px;
  text-align: center;
  border: none;
  right: 0;
  bottom: 0;
}

.user-area .profile-img-file {
  display: none;
}

.user-area .sidebar-list li {
  margin: 5px 0;
}

.user-area .sidebar-list li a {
  color: var(--color-dark);
  padding: 6px 6px 6px 15px;
  display: block;
  font-weight: 500;
  transition: var(--transition);
}

.user-area .sidebar-list li a:hover {
  padding-left: 20px;
}

.user-area .sidebar-list li a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.user-area .sidebar-list .active i {
  color: var(--color-white);
}

.user-area .sidebar-list .active {
  background: var(--theme-color);
  color: var(--color-white);
  border-radius: 8px;
}

.user-area .sidebar-list li a span{
  float: right;
  line-height: unset;
  padding: 0px 6px;
  border-radius: 10px;
  margin-top: 3.7px;
}


/* user card */
.user-card {
  background: var(--color-white);
  padding: 15px 20px 20px 20px;
  border-radius: 10px;
  margin-bottom: 25px;
}

.user-card-title {
  color: var(--color-dark);
  font-size: 20px;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.user-card .text-success {
  color: var(--theme-color) !important;
}

.user-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.user-card-header .user-card-title {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.user-card-header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-card-header .theme-btn {
  padding: 8px 15px;
}

@media all and (max-width: 767px) {
  .user-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .user-card-header-right {
    flex-direction: column-reverse;
    align-items: flex-start;
  }
}


/* user card search */
.user-card-search .form-group {
  position: relative;
}

.user-card-search .form-control {
  padding: 10px 20px 10px 35px;
  box-shadow: none;
  border-radius: 10px;
  border-color: var(--border-info-color);
}

.user-card-search .form-control:focus {
  border-color: var(--theme-color);
}

.user-card-search i {
  position: absolute;
  left: 12px;
  top: 15px;
}

.user-card-filter .nice-select{
  height: 45px;
  line-height: 42.5px;
  border-color: var(--border-info-color);
  border-radius: 10px;
}

.user-card-filter .nice-select::after{
  width: 6px;
  height: 6px;
  margin-top: -5px;
}

/* user form */
.user-form .form-group {
  margin-bottom: 15px;
}

.user-form .form-group label {
  color: var(--color-dark);
  margin-bottom: 4px;
}

.user-form .form-control {
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: none;
}

.user-form .nice-select{
  width: 100%;
  height: 50px;
  line-height: 48px;
  border-radius: 10px;
  font-size: 16px;
  padding-left: 20px;
  margin-bottom: 15px;
}

.user-form .nice-select::after{
  width: 9px;
  height: 9px;
  margin-top: -7px;
  right: 18px;
}

.user-form .nice-select .list{
  width: 100%;
}

.user-form .nice-select:focus,
.user-form .form-control:focus {
  border-color: var(--theme-color);
}

.user-form .theme-btn {
  width: unset;
  margin-top: 15px;
}

.user-form .form-img-upload {
  width: 100%;
  height: 140px;
  border: 2px dashed var(--border-info-color);
  border-radius: 10px;
  cursor: pointer;
}

.user-form .form-img-file {
  display: none;
}

.user-form .form-img-upload span {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.user-form .form-img-upload span i {
  margin-right: 5px;
}

.user-form .form-check {
  margin-bottom: 15px;
}

.user-form .form-check-input {
  margin-top: 6.5px;
  box-shadow: none;
}

.user-form .form-check-input:focus {
  border-color: var(--theme-color);
}

.user-form .form-check label {
  color: var(--color-dark);
}

.user-form .form-check label a{
  color: var(--theme-color);
}

/* user badge */
.user-area .badge{
  border-radius: 50px;
  padding: 5px 12px;
}

.user-area .badge-success{
  background: #DCFCE7;
  color: #22C79C;
}

.user-area .badge-info{
  background: #F3E8FF;
  color: #A855F7;
}

.user-area .badge-primary{
  background: #DBF7FD;
  color: #0DCAF0;
}

.user-area .badge-danger{
  background: #FEE2E2;
  color: #EF4444;
}

/* dashboard */
.dashboard-widget {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 20px;
  border-radius: 10px;
  margin: 10px 0;
}

.dashboard-widget-info span {
  font-weight: 500;
}

.dashboard-widget-info h1 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 5px;
}

.dashboard-widget-icon {
  width: 65px;
  height: 65px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  border-radius: 50px;
}

.dashboard-widget.color-1 {
  background: rgba(168, 85, 247, .08);
  color: #A855F7;
}

.dashboard-widget.color-1 h1 {
  color: #A855F7;
}

.dashboard-widget.color-1 .dashboard-widget-icon {
  background: #A855F7;
  color: #fff;
}

.dashboard-widget.color-2 {
  background: rgba(17, 183, 107, .08);
  color: #11B76B;
}

.dashboard-widget.color-2 h1 {
  color: #11B76B;
}

.dashboard-widget.color-2 .dashboard-widget-icon {
  background: #11B76B;
  color: #fff;
}

.dashboard-widget.color-3 {
  background: rgba(240, 84, 84, .08);
  color: #F05454;
}

.dashboard-widget.color-3 h1 {
  color: #F05454;
}

.dashboard-widget.color-3 .dashboard-widget-icon {
  background: #F05454;
  color: #fff;
}

/* user table */
.user-area .table {
  border-collapse: separate;
  border-spacing: 0 10px;
  margin-bottom: 0;
}

.user-area .table th{
  color: var(--body-text-color);
}

.user-area .table tbody tr{
  background: var(--theme-bg-light);
  margin-bottom: 10px;
}

.user-area .table td{
  background: unset;
}

.user-area .table td:first-child {
  border-left-style: solid;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.user-area .table td:last-child {
  border-right-style: solid;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.user-area .table-list-info a {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-area .table-list-info h6 {
  transition: all .5s ease-in-out;
  margin-bottom: 5px;
}

.user-area .table-list-content:hover h6 {
  color: var(--theme-color);
}

.user-area .table-list-info p{
  color: var(--color-dark);
}

.user-area .table-list-info span{
  color: var(--color-dark);
}

.user-area .table-list-img{
  background: var(--color-white);
  border-radius: 10px;
  padding: 10px;
}

.user-area .table-list-info img {
  width: 80px;
  border-radius: 8px;
}

.user-area .user-card .table>:not(caption)>*>* {
  vertical-align: middle;
  padding: 0.8rem 0.8rem;
}

.user-area .table-list-code{
  color: var(--theme-color);
  font-weight: 500;
}

@media all and (max-width: 767px) {
  .user-area .table-list-info a {
    display: block;
  }

  .user-area .table-list-info h6 {
    margin-top: 10px;
  }
}

/* user action dropdown */
.user-action-dropdown .btn{
  padding: 4px 9px;
  border-radius: 8px;
}

.user-action-dropdown .dropdown-menu{
  border: none;
  box-shadow: var(--box-shadow);
  z-index: 1;
}

.user-action-dropdown .dropdown-item:hover{
  background: transparent;
  color: var(--theme-color);
}

.user-action-dropdown .dropdown-item i{
  margin-right: 5px;
}

/* user order details */
.user-order-detail .order-detail-content{
  background: var(--theme-bg-light);
  padding: 30px;
  border-radius: 10px;
  margin-top: 15px;
}

.user-order-detail .order-detail-content h5{
  margin-bottom: 20px;
}

.user-order-detail .order-detail-content p i{
  margin-right: 5px;
}

.user-order-detail .order-detail-content li{
  margin: 5px 0;
}

.user-order-detail .order-detail-content li span{
  float: right;
}

.user-order-detail .order-detail-content li:last-child{
  border-top: 1px solid var(--border-info-color);
  margin-top: 10px;
  padding-top: 10px;
  font-weight: 500;
}

/* user ticket details */
.user-ticket-detail .ticket-detail-content{
  margin-top: 30px;
}

.user-ticket-detail .ticket-chat-item{
  display: flex;
  gap: 20px;
  margin: 50px 0;
}

.user-ticket-detail .ticket-img img{
  width: 80px;
  border-radius: 50%;
}

.user-ticket-detail .ticket-info{
  flex: 1;
}

.user-ticket-detail .ticket-info span{
  color: var(--theme-color);
}

/* user order track */
.user-track-order .track-order-content{
  margin-top: 30px;
}

.user-track-order .track-order-content h5{
  text-transform: uppercase;
  font-size: 15px;
  letter-spacing: 1px;
}

.user-track-order .track-order-content h5 span{
  color: var(--theme-color);
}

.user-track-order .track-order-info{
  margin-top: 25px;
}

.user-track-order .track-order-info a{
  background: var(--theme-bg-light);
  padding: 10px 20px;
  border-radius: 10px;
  margin: 0 5px 5px 0;
}

.user-track-order .track-order-info a span{
  font-weight: 500;
}

.user-track-order .track-order-step{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 30px;
}

.user-track-order .track-order-step .step-item{
  text-align: center;
  padding: 10px;
  flex: 1;
  position: relative;
  z-index: 1;
}

.user-track-order .track-order-step .step-item::before{
  content: "";
  position: absolute;
  width: 100%;
  height: 5px;
  background: var(--theme-color);
  left: 0;
  top: 50px;
  z-index: -1;
}

.user-track-order .track-order-step .step-item:first-child:before{
  width: 50%;
  left: unset;
  right: 0;
}

.user-track-order .track-order-step .step-item:last-child:before{
  width: 50%;
}

.user-track-order .track-order-step .step-item.completed::before{
  background: var(--color-red);
}

.user-track-order .track-order-step .step-icon{
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  background: var(--theme-color);
  color: var(--color-white);
  font-size: 35px;
  border-radius: 50%;
  margin: 0 auto;
}

.user-track-order .track-order-step .step-item.completed .step-icon{
  background: var(--color-red);
}

.user-track-order .track-order-step .step-item h6{
  margin-top: 15px;
  font-size: 14px;
}

@media all and (max-width: 767px) {
  .user-track-order .track-order-step{
    flex-wrap: nowrap;
    flex-direction: column;
  }
  .user-track-order .track-order-step .step-item::before{
    display: none;
  }
}

/* user setting */
.user-setting .form-check{
  margin-bottom: 10px;
}

.user-setting .form-check-input{
  margin-top: 6.5px;
  box-shadow: none;
}

.user-setting .form-check-label{
  color: var(--body-text-color);
}

/* user message */
.user-message .message-wrapper {
  display: flex;
}

.user-message .message-inbox {
  max-width: 340px;
  border: 1px solid var(--border-info-color);
  border-radius: 10px;
}

.user-message .message-inbox {
  overflow: hidden;
}

.user-message .message-content {
  padding-left: 30px;
  position: relative;
  overflow: hidden;
}

.user-message .message-content-info {
  max-height: 750px;
  overflow-y: scroll;
  margin-right: -50px;
  padding-right: 50px;
}

.user-message .message-inbox,
.user-message .message-content {
  flex: 1;
}

.user-message .message-inbox ul {
  max-height: 950px;
  overflow-y: scroll;
  width: 357px;
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-message .message-inbox ul li.message-active {
  border-left: 3px solid var(--theme-color);
}

.user-message .message-inbox ul li {
  border-bottom: 1px solid var(--border-info-color);
  transition: .2s;
  list-style: none;
}

.user-message .message-inbox ul li:last-child {
  border-bottom: none;
}

.user-message .message-by-content h5 {
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1;
}

.user-message .message-inbox ul li a {
  position: relative;
  display: block;
  padding: 30px;
}

.user-message .message-inbox .message-avatar {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.user-message .message-avatar img {
  display: inline-block;
  width: 54px;
  height: 54px;
  border-radius: 50%;
}

.user-message .message-avatar img {
  width: 50px;
  height: 50px;
}

.user-message .message-inbox .message-by {
  margin-left: 50px;
}

.user-message .message-by-content h5 i {
  background: var(--color-yellow);
  padding: 3px 8px;
  border-radius: 50px;
  color: var(--color-white);
  font-size: 13px;
}

.user-message .message-by-content span {
  font-size: 13px;
  position: absolute;
  top: 25.5px;
  right: 25px;
  float: right;
  color: var(--body-text-color);
}

.user-message .message-inbox .message-by p {
  height: 26px;
  max-width: 205px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 14px;
  margin: 0;
  padding: 0;
  color: var(--body-text-color);
  line-height: 27px;
}

.user-message .message-item {
  display: block;
  position: relative;
  margin-bottom: 25px;
}

.user-message .message-item .message-avatar {
  position: absolute;
  left: 0;
  top: 0;
}

.user-message .message-item .message-avatar img {
  width: 50px;
  height: 50px;
  display: inline-block;
  border-radius: 50%;
}

.user-message .message-item .message-description {
  margin-left: 70px;
  background: var(--theme-bg-light);
  border-radius: 12px;
  padding: 20px;
  position: relative;
}

.user-message .message-item .message-description::before {
  position: absolute;
  top: 0px;
  left: -9px;
  content: "\f0d9";
  font-family: "Font Awesome 6 Pro";
  font-weight: bold;
  font-size: 31px;
  color: var(--theme-bg-light);
}

.user-message .message-item .message-description p {
  font-size: 15px;
  padding: 0;
  margin: 0;
  line-height: 27px;
}

.user-message .message-item.me .message-avatar {
  left: auto;
  right: 0;
  position: absolute;
  top: 0;
}

.user-message .message-item.me .message-description {
  color: var(--theme-color);
  background: #F2F7FF;
  margin-left: 0;
  margin-right: 70px;
  border-radius: 12px;
  padding: 20px;
  position: relative;
}

.user-message .message-item.me .message-description::before {
  content: "\f0da";
  left: auto;
  right: -10px;
  color: #F2F7FF;
}

.user-message .message-reply {
  margin-top: 15px;
  position: absolute;
  bottom: 0;
  left: 30px;
  right: 15px;
}

.user-message .message-reply .form-control {
  padding: 15px 20px;
  box-shadow: none;
  border-radius: 10px;
}

.user-message .message-reply .form-control:focus {
  border-color: var(--theme-color);
}

.user-message .message-reply .theme-btn {
  margin-top: 15px;
}

.user-message .message-status {
  width: 12px;
  height: 12px;
  background: var(--theme-color);
  display: flex;
  border-radius: 50%;
  border: 3px solid var(--color-white);
  position: absolute;
  right: -5px;
  top: 50%;
}

.user-message .message-status.online {
  background: var(--theme-color);
}

.user-message .message-status.offline {
  background: var(--color-red);
}

.user-message .message-status.busy {
  background: var(--color-yellow);
}

.user-message .header-account:hover{
  cursor: pointer;
}

.user-message .header-account img {
  width: 45px;
  border-radius: 50%;
}

.user-message .header-account .dropdown-menu{
  border: none;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.user-message .header-account .dropdown-item:hover{
  background: transparent;
  color: var(--theme-color);
  padding-left: 20px;
}


@media all and (max-width: 991px) {
  .user-message .message-by-content span {
    right: 65px;
  }

  .user-message .message-reply {
    right: 0;
  }
}

@media all and (max-width: 767px) {
  .user-message .message-wrapper {
    flex-direction: column;
    gap: 50px;
  }

  .user-message .user-card-header {
    flex-direction: row;
    align-items: center;
    gap: 20px;
  }

  .user-message .message-content {
    padding-left: 0;
    padding-bottom: 200px;
  }

  .user-message .message-reply {
    left: 0;
  }
}



/*====================
47. Coming soon css 
======================*/

.coming-soon {
  background-repeat: no-repeat;
  background-size: cover !important;
  background-position: center !important;
  min-height: 100vh;
  width: 100%;
  top: 0;
  left: 0;
  position: relative;
}

.coming-soon .container {
  position: relative;
}

.coming-soon:before {
  position: absolute;
  content: '';
  background: rgba(0, 0, 0, .7);
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.coming-soon-content .title{
  margin-bottom: 8px;
}

.coming-soon-content .lead{
  font-weight: 500;
}

.coming-soon .newsletter-form {
  position: relative;
  margin-top: 30px;
}

.coming-soon .newsletter-form .input-newsletter {
  height: 55px;
  display: block;
  width: 100%;
  border: none;
  border-radius: 15px;
  font-size: 14px;
  padding-top: 0;
  padding-right: 150px;
  padding-bottom: 0;
  padding-left: 25px;
  box-shadow: none;
}

.coming-soon .newsletter-form button {
  position: absolute;
  right: 4px;
  top: 4px;
  bottom: 4px;
  height: auto;
  border: none;
  border-radius: 12px;
  background: var(--theme-color);
  display: inline-block;
  color: var(--color-white);
  padding-left: 30px;
  padding-right: 30px;
  font-weight: 500;
  transition: .5s;
}

.coming-soon .newsletter-form button:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.coming-social {
  margin-top: 30px;
  text-align: center;
}

.coming-social a {
  color: var(--color-white);
  margin: 5px;
  width: 40px;
  height: 40px;
  line-height: 36px;
  text-align: center;
  border-radius: 12px;
  border: 2px solid var(--color-white);
  display: inline-block;
  transition: .5s;
}

.coming-social a:hover {
  background: var(--theme-color);
  border-color: var(--theme-color);
}

.coming-soon-countdown {
  margin-top: 30px;
}

@media all and (max-width: 1399px) {
  .coming-soon-content{
    margin-top: 0;
  }
}


/*====================
48. Error css 
======================*/

.error-wrapper {
  text-align: center;
}

.error-wrapper h1 {
  font-size: 250px;
  letter-spacing: 5px;
  font-weight: bold;
  color: var(--theme-color);
}

.error-wrapper h1 span {
  color:  var(--color-dark);
}

.error-wrapper h2 {
  margin-top: 30px;
  margin-bottom: 10px;
}

.error-wrapper img {
  width: 100%;
}

.error-wrapper .theme-btn {
  margin-top: 30px;
}


@media all and (max-width: 767px) {
  .error-wrapper h1 {
    font-size: 160px;
  }
}




/*====================
49. Terms/privacy css 
====================== */

.terms-content:not(:last-child) {
  margin-bottom: 54px;
}

.terms-content:first-child {
  margin-top: -3px;
}

.terms-content .terms-list {
  margin-top: 37px;
}

.terms-content h3 {
  position: relative;
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 15px;
}

.terms-content p:not(:last-child) {
  margin-bottom: 26px;
}

.terms-list li:not(:last-child) {
  margin-bottom: 16px;
}



/*============================
50. Mail success css 
==============================*/

.mail-success{
  position: relative;
}

.mail-success-content{
  background: var(--color-white);
  padding: 50px;
  text-align: center;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
}

.mail-success-icon{
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 35px;
  color: var(--color-white);
  background: var(--theme-color);
  margin: 0 auto;
  border-radius: 50px;
  margin-bottom: 20px;
}

.mail-success-content h3{
  text-transform: capitalize;
}

.mail-success-content p{
  margin-top: 15px;
  margin-bottom: 30px;
}




/*====================
51. Blog css 
======================*/

.blog-area{
  position: relative;
}

.blog-item{
  background: var(--color-white);
  transition: var(--transition);
}

.blog-item-img{
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.blog-item-img img {
  border-radius: 15px;
  box-shadow: var(--box-shadow);
}

.blog-item:hover .blog-item-img img{
  transform: scale(1.1);
} 

.blog-date{
  position: absolute;
  right: 0;
  bottom: 0;
  background: var(--theme-color2);
  color: var(--color-dark);
  font-weight: 500;
  border-radius: 50px 0 0 0;
  padding: 4px 12px 4px 22px;
}

.blog-item-meta{
  padding: 14px 0;
  margin-bottom: 14px;
  border-bottom: 1px solid var(--border-info-color);
}

.blog-item-meta ul li {
  display: inline-block;
  margin-right: 15px;
  font-weight: 500;
  position: relative;
  color:  var(--color-dark);
}

.blog-item-meta ul li i {
  margin-right: 5px;
  color: var(--theme-color);
}

.blog-item-meta a:hover {
  color: var(--theme-color);
}

.blog-title {
  font-size: 20px;
  margin-bottom: 15px;
  text-transform: capitalize;
}

.blog-item-info p {
  margin-bottom: 16px;
}

.blog-item-info .theme-btn {
  margin-top: 10px;
}

.blog-item-info h4 a {
  color:  var(--color-dark);
}

.blog-item-info h4 a:hover {
  color: var(--theme-color);
}



/*========================
52. Blog single css
==========================*/

.blog-thumb-img {
  margin-bottom: 20px;
}

.blog-single-content img {
  border-radius: 15px;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.blog-meta .blog-meta-left ul {
  display: flex;
  align-items: center;
  gap: 20px;
}

.blog-meta .blog-meta-left ul li{
  font-weight: 500;
}

.blog-meta i {
  margin-right: 5px;
  color: var(--theme-color);
}

.blog-meta a {
  color: var(--body-text-color);
  font-weight: 500;
}

.blog-meta a:hover {
  color: var(--theme-color);
}

.blog-details-title {
  font-size: 34px;
  color:  var(--color-dark);
}

.blockqoute {
  position: relative;
  background: var(--theme-bg-light);
  border-left: 5px solid var(--theme-color);
  padding: 30px;
  font-size: 17px;
  font-style: italic;
  margin: 20px 0;
  border-radius: 0px;
}

.blockqoute i{
  position: absolute;
  right: 30px;
  bottom: 8px;
  color: var(--theme-color);
  font-size: 90px;
  opacity: .2;
}

.blockqoute-author {
  margin-top: 20px;
  padding-left: 60px;
  position: relative;
  color:  var(--color-dark);
}

.blockqoute-author::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 40px;
  background: var(--theme-color);
  left: 0;
  top: 10px;
}

.blog-details-tags {
  display: flex;
  align-items: center;
  gap: 20px;
}

.blog-details-tags h5 {
  color:  var(--color-dark);
}

.blog-details-tags ul {
  display: flex;
  align-items: center;
  gap: 15px;
}

.blog-details-tags ul a {
  background: var(--theme-bg-light);
  color:  var(--color-dark);
  padding: 4px 18px 5px 18px;
  border-radius: 50px;
  transition: var(--transition);
}

.blog-details-tags ul a:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.blog-author {
  display: flex;
  justify-content: start;
  align-items: center;
  background: var(--color-white);
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  margin: 50px 0;
  padding: 20px;
}

.blog-author-img {
  width: 320px;
}

.blog-author-img img {
  border-radius: 15px;
}

.author-name {
  font-size: 22px;
  color: var(--theme-color);
  margin: 8px 0;
}

.author-info {
  padding: 0 20px;
}

.author-social {
  margin-top: 10px;
}

.author-social a {
  width: 35px;
  height: 35px;
  line-height: 31px;
  text-align: center;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  margin-right: 5px;
  color: var(--theme-color);
  transition: var(--transition);
}

.author-social a:hover {
  color: var(--color-white);
  background: var(--theme-color);
}

.blog-comments {
  margin-bottom: 50px;
}

.blog-comments h3 {
  color:  var(--color-dark);
}

.blog-comments-wrap {
  margin: 30px 0;
}

.blog-comments-item {
  display: flex;
  justify-content: start;
  align-items: flex-start;
  margin-top: 50px;
}

.blog-comments-item img {
  border-radius: 50%;
}

.blog-comments-content {
  padding: 0 0 0 20px;
}

.blog-comments-content span {
  font-size: 14px;
  color: var(--theme-color);
  font-weight: 500;
}

.blog-comments-content a {
  font-weight: 500;
  margin-top: 5px;
  color: var(--theme-color);
}

.blog-comments-content a:hover {
  color:  var(--color-red);
}

.blog-comments-content h5 {
  color:  var(--color-dark);
}

.blog-comments-reply {
  margin-left: 50px;
}

.blog-comments-form {
  padding: 30px;
  margin-top: 50px;
  border-radius: 10px;
  background: var(--theme-bg-light);
}

.blog-comments-form h3 {
  margin-bottom: 20px;
}

.blog-comments-form .form-group {
  margin-bottom: 20px;
}

.blog-comments-form .form-control {
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: none;
  transition: var(--transition);
}

.blog-comments-form .form-control:focus {
  border-color: var(--theme-color);
}


@media all and (max-width: 767px) {
  .blog-meta {
    flex-direction: column;
    font-size: 15px;
  }

  .blog-meta .blog-meta-left ul {
    gap: 10px;
  }

  .blog-details-tags {
    flex-direction: column;
    align-items: flex-start;
  }

  .blog-author {
    flex-direction: column;
    text-align: center;
    padding: 25px;
  }

  .author-info{
    margin-top: 25px;
  }

  .blog-comments-item {
    flex-direction: column;
    text-align: center;
    padding: 30px 0px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    border-radius: 10px;
  }

  .blog-comments-item img{
    margin: 0 auto 20px auto;
  }

  .blog-comments-reply {
    margin-left: 0px;
  }
}



/*=======================
53. Widget sidebar css
=========================*/

.widget {
  background: var(--theme-bg-light);
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 15px;
}

.widget .widget-title {
  position: relative;
  padding-bottom: 10px;
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 700;
  color:  var(--color-dark);
}

.widget .widget-title::before {
  position: absolute;
  content: '';
  width: 10px;
  border-bottom: 3px solid var(--theme-color);
  bottom: 0;
  left: 0;
}

.widget .widget-title::after {
  position: absolute;
  content: '';
  width: 25px;
  border-bottom: 3px solid var(--theme-color);
  bottom: 0;
  left: 15px;
}

.widget .search-form .form-control {
  padding: 12px 15px 12px 15px;
  border-radius: 12px;
  box-shadow: none;
}

.widget .search-form {
  position: relative;
}

.widget .search-form .form-control:focus {
  border-color: var(--theme-color);
}

.widget .search-form button {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 18px;
  padding: 8px 18px 6px 18px;
  background: transparent;
  border: none;
  color: var(--theme-color);
}

.widget .category-list a {
  display: block;
  padding: 10px 0;
  font-weight: 500;
  border-bottom: 1px solid var(--border-info-color);
  transition: var(--transition);
}

.widget .category-list a:last-child {
  margin-bottom: 0px;
  border-bottom: none;
}

.widget .category-list a:hover {
  padding-left: 10px;
  color: var(--theme-color);
}

.widget .category-list a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.widget .category-list a span {
  float: right;
}

.widget .recent-post-item {
  display: flex;
  justify-content: start;
  align-items: center;
  margin-bottom: 20px;
}

.widget .recent-post-item:last-child{
  margin-bottom: 0;
}

.widget .recent-post-img {
  margin-right: 20px;
}

.widget .recent-post-img img {
  width: 120px;
  border-radius: 12px;
}

.widget .recent-post-bio h6 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 4px;
  text-transform: capitalize;
}

.widget .recent-post-bio span {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-color);
}

.widget .recent-post-bio span i {
  margin-right: 5px;
}

.widget .recent-post-bio h6 a:hover {
  color: var(--theme-color);
}

.widget .social-share-link a {
  width: 35px;
  height: 35px;
  line-height: 32px;
  border: 2px solid var(--theme-color);
  color: var(--theme-color);
  text-align: center;
  margin-right: 5px;
  border-radius: 50px;
  transition: var(--transition);
}

.widget .social-share-link a:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.widget .tag-list a {
  background: var(--color-white);
  color:  var(--color-dark);
  padding: 5px 15px;
  margin-bottom: 10px;
  margin-right: 10px;
  border-radius: 8px;
  display: inline-block;
  transition: var(--transition);
}

.widget .tag-list a:hover {
  background-color: var(--theme-color);
  color: var(--color-white);
}



/*====================
54. Pagination css 
======================*/

.pagination-area{
  position: relative;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pagination .page-link {
  border: none;
  background:  var(--color-dark);
  color: var(--color-white);
  margin: 0 10px;
  border-radius: 10px !important;
  width: 40px;
  height: 40px;
  line-height: 28px;
  text-align: center;
  transition: var(--transition);
}

.pagination .page-link:hover,
.pagination .page-item.active .page-link {
  background: var(--theme-color);
  color: var(--color-white);
  z-index: 1;
}



/*====================
55. Breadcrumb css 
======================*/

.site-breadcrumb {
  position: relative;
  background-repeat: no-repeat !important;
  background-position: center !important;
  background-size: cover !important;
  padding-top: 50px;
  padding-bottom: 50px;
  z-index: 1;
}

.site-breadcrumb-bg {
  position: absolute;
  background-repeat: no-repeat;
  background-position: center !important;
  background-size: cover !important;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.site-breadcrumb .breadcrumb-title {
  font-size: 25px;
  color: var(--color-white);
  font-weight: 700;
  margin-bottom: 10px;
  text-transform: capitalize;
}

.site-breadcrumb .breadcrumb-menu {
  position: relative;
  z-index: 1;
}

.site-breadcrumb .breadcrumb-menu li {
  position: relative;
  display: inline-block;
  margin-left: 25px;
  color: var(--color-dark);
  font-weight: 500;
  text-transform: capitalize;
}

.site-breadcrumb .breadcrumb-menu li a {
  color: var(--color-white);
  transition: all 0.5s ease-in-out;
}

.site-breadcrumb .breadcrumb-menu li::before {
  position: absolute;
  content: '\f101';
  font-family: 'Font Awesome 6 Pro';
  right: -21px;
  top: 1px;
  text-align: center;
  font-size: 16px;
  color: var(--color-white);
}

.site-breadcrumb .breadcrumb-menu li:first-child {
  margin-left: 0;
}

.site-breadcrumb .breadcrumb-menu li:last-child:before {
  display: none;
}

.site-breadcrumb .breadcrumb-menu li a:hover {
  color: var(--theme-color2);
}

.site-breadcrumb .breadcrumb-menu li.active {
  color: var(--theme-color2);
}



/*===================
56. Contact us css 
=====================*/

.contact-area {
  position: relative;
}

.contact-content {
  margin-bottom: 50px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
  padding: 25px 15px;
  position: relative;
  margin-bottom: 25px;
  border-radius: 20px;
  background:var(--color-white);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.contact-info:hover{
  transform: translateY(-8px)
}

.contact-info-icon i {
  font-size: 35px;
  color: var(--color-white);
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 50px;
  background: var(--theme-color);
}

.contact-info h5 {
  font-size: 18px;
  margin-bottom: 8px;
  color: var(--color-dark);
}

.contact-info p{
  color: var(--body-text-color);
  font-weight: 500;
  font-size: 16px;
}

.contact-form{
  background: var(--color-white);
  border-radius: 20px;
  padding: 30px 30px 10px 30px;
  box-shadow: var(--box-shadow);
}

.contact-form-header {
  margin-bottom: 30px;
}

.contact-form-header h2 {
  font-size: 25px;
  font-weight: 700;
  margin-bottom: 10px;
  color:  var(--color-dark);
}

.contact-form .form-group {
  margin-bottom: 25px;
}

.contact-form .form-group .form-control {
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: none;
  transition: var(--transition);
}

.contact-form .form-group .form-control:focus {
  border-color: var(--theme-color);
}

.contact-map {
  margin-bottom: -9px;
}

.contact-map iframe {
  width: 100%;
  height: 350px;
}


@media all and (max-width: 991px) {
  .contact-info{
    padding: 35px;
  }
}

@media all and (max-width: 768px) {
  .contact-content {
    margin-top: 50px;
    margin-bottom: 0;
  }
}



/* ========================
57. Gallery css 
=========================== */

.gallery-item {
  position: relative;
  width: 100%;
  text-align: center;
}

.gallery-img {
  position: relative;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
}

.gallery-img::before{
  content:"";
  position:absolute;
  inset:0;
  background: var(--theme-color2);
  transition: all 0.7s ease-in-out;
  transform: translateY(-110%);
}

.gallery-img:hover::before{
  transform:translateY(110%);
}

.gallery-img::after{
  content:"";
  position: absolute;
  inset: 0;
  background: var(--theme-color2);
  opacity: .8;
  transition: all 0.7s ease-in-out;
  transform: translateY(-110%);
}

.gallery-img:hover::after{
  transform: none;
}

.gallery-img img {
  width: 100%;
  border-radius: 15px;
}

.gallery-link {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  font-size: 34px;
  color: var(--color-yellow);
  width: 60px;
  height: 60px;
  line-height: 60px;
  background: var(--color-white);
  transition: all 200ms 0ms cubic-bezier(0.6, -0.28, 0.735, 0.045);
  transform: translate(-50%, -50%) scale(0);
  z-index: 1;
}

.gallery-item:hover .gallery-link {
  transform: translate(-50%, -50%) scale(1);
  transition: all 300ms 100ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transition-delay: 0.7s;
}

/* gallery-img-item */
.gallery-img-item{
  position: absolute;
  top: 120px;
  left: 150px;
}

.gallery-img-content{
  position: absolute;
  width: 250px;
  left: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--color-white);
  padding: 10px;
  margin-top: 10px;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.gallery-img-item:hover .gallery-img-content{
  opacity: 1;
  visibility: visible;
  margin-top: 0px;
}

.gallery-img-content img{
  width: 80px;
  height: 80px;
}

.gallery-img-info{
  flex: 1;
  text-align: left;
}

.gallery-img-info h6 a{
  color: var(--color-dark);
}

.gallery-img-info h6 a:hover{
  color: var(--theme-color);
}

.gallery-img-info p{
  margin-top: 5px;
  margin-bottom: 5px;
}

.gallery-img-info p del{
  margin-right: 8px;
}

.gallery-img-info p span{
  color: var(--color-red);
  font-weight: 500;
}

.gallery-img-info .theme-btn{
  padding: 3px 12px;
}

.gallery-img-item button{
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: var(--theme-color2);
  border-radius: 50px;
  text-align: center;
  color: var(--color-dark);
  font-size: 25px;
  border: none;
  animation: gallery-pulse 2s infinite;
}

@keyframes gallery-pulse {
  0%{
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 1);
  }

  100%{
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }
}

.gallery-btn-active .gallery-img::before,
.gallery-btn-active .gallery-img::after{
  display: none;
}

.gallery-item-1{
  top: unset;
  left: 50px;
  bottom: 140px;
}

.gallery-item-2{
  top: unset;
  left: unset;
  right: 120px;
  bottom: 150px;
}

.gallery-item-2 .gallery-img-content{
  left: unset;
  right: 0;
}


@media all and (min-width: 991px) and (max-width: 1199px) {
  .gallery-item-1{
    bottom: 120px;
  }
  
  .gallery-item-2{
    bottom: 130px;
    right: 50px;
  }
}


@media all and (max-width: 767px) {
  .gallery-btn-active .main-img{
    width: 100%;
    height: 300px;
    object-fit: cover;
  }

  .gallery-img-content{
    margin-top: 0;
  }

  .gallery-item-1{
    left: 20px;
    bottom: 70px;
  }

  .gallery-item-1 .gallery-img-content{
    left: 0px;
    bottom: 40px;
  }

  .gallery-item-2{
    right: 20px;
    bottom: 130px;
  }

  .gallery-item-2 .gallery-img-content{
    bottom: 40px;
  }
}



/* ===================
58. Affiliate css 
====================== */

.affiliate-area{
  position: relative;
}

.affiliate-img{
  position: relative;
}

.affiliate-img::before{
  content: "";
  position: absolute;
  right: 30px;
  bottom: 0;
  width: 50%;
  height: 100%;
  background: var(--theme-color2);
  transform: rotate(5deg);
  border-radius: 20px;
  z-index: -1;
}

.affiliate-img img{
  border-radius: 20px;
  width: 90%;
}

.affiliate-item{
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  background: var(--theme-bg-light);
}

.affiliate-percentage{
  width: 120px;
  height: 120px;
  border-radius: 50%;
  text-align: center;
  border: 5px solid var(--color-white);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: var(--theme-color2);
}

.affiliate-percentage h1{
  color: var(--color-dark);
  font-weight: bold;

}

.affiliate-percentage h1 span{
  font-size: 20px;
  color: var(--color-dark);
}

.affiliate-item .level{
  color: var(--color-dark);
  font-weight: 500;
}


@media all and (max-width: 991px) {
  .affiliate-img{
    margin-bottom: 35px;
  }
}



/* ===================
59. Cta css 
====================== */

.cta-area{
  position: relative;
  background: var(--theme-color);
  overflow: hidden;
  z-index: 1;
}

.cta-area::before{
  content: "";
  position: absolute;
  width: 250px;
  height: 250px;
  border: 30px solid var(--color-white);
  border-radius: 50%;
  left: 50px;
  bottom: -100px;
  opacity: .1;
  z-index: -1;
}

.cta-area::after{
  content: "";
  position: absolute;
  width: 350px;
  height: 350px;
  border: 30px solid var(--color-white);
  border-radius: 50%;
  right: 50px;
  top: -150px;
  opacity: .1;
  z-index: -1;
}

.cta-content{
  text-align: center;
}

.cta-content h6{
  color: var(--theme-color2);
  text-transform: uppercase;
  letter-spacing: 5px;
}

.cta-content h1{
  color: var(--color-white);
  font-weight: 700;
  margin-top: 25px;
  margin-bottom: 15px;
}

.cta-content h1 span{
  color: var(--theme-color2);
}

.cta-content p{
  color: var(--color-white);
}

.cta-content .theme-btn{
  margin-top: 25px;
}



/* ===================
60. Process css 
====================== */

.process-area{
  position: relative;
}

.process-item{
  padding: 25px;
  border: 1px solid var(--border-info-color);
  border-radius: 15px;
  position: relative;
}

.process-item .icon{
  width: 80px;
  height: 80px;
  line-height: 75px;
  text-align: center;
  background: var(--theme-color2);
  border-radius: 50px;
  margin-bottom: 20px;
}

.process-item .icon img{
  width: 45px;
}

.process-item .icon span{
  position: absolute;
  right: 20px;
  top: 0;
  font-weight: 800;
  font-size: 40px;
  color: transparent;
  -webkit-text-stroke: 1px var(--theme-color);
}

.process-item h4{
  color: var(--color-dark);
  margin-bottom: 10px;
}



/* ===================
61. Instagram css 
====================== */

.instagram-area{
  position: relative;
}

.instagram-item {
  position: relative;
}

.instagram-img img{
  border-radius: 15px;
}

.instagram-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  opacity: 0;
  z-index: 0;
  transition: 0.4s;
}

.instagram-item a {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 45px;
  height: 45px;
  line-height: 45px;
  border-radius: 50%;
  background: var(--theme-color);
  color: var(--color-white);
  font-size: 20px;
  visibility: hidden;
  opacity: 0;
  text-align: center;
  z-index: 2;
  transition: 0.4s;
  transform: translate(-50%, -50%);
}

.instagram-item:hover::after {
  width: 100%;
  opacity: 1;
}

.instagram-item:hover a {
  visibility: visible;
  opacity: 1;
}



/*====================
62. Choose css 
======================*/

.choose-area{
  position: relative;
}

.choose-img img{
  border-radius: 100px;
  box-shadow: var(--box-shadow);
}

.choose-content{
  margin-top: 70px;
}

.choose-item{
  display: flex;
  gap: 18px;
}

.choose-icon{
  width: 80px;
  height: 80px;
  line-height: 76px;
  text-align: center;
  background: var(--theme-color2);
  border-radius: 50px;
  box-shadow: var(--box-shadow);
}

.choose-icon img{
  width: 50px;
}

.choose-info{
  flex: 1;
}

.choose-info h4{
  color: var(--color-dark);
  margin-bottom: 8px;
}

.choose-info p{
  color: var(--body-text-color);
}




/*====================
63. Newsletter css 
======================*/

.newsletter-area{
  position: relative;
}

.newsletter-wrap{
  position: relative;
  background-image: url(../img/newsletter/01.jpg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 80px 0;
  border-radius: 15px;
  z-index: 1;
}

.newsletter-wrap::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(34, 41, 52, .7);
  border-radius: 15px;
  z-index: -1;
}

.newsletter-content{
  text-align: center;
}

.newsletter-content h3{
  font-size: 25px;
  font-weight: 700;
  margin-bottom: 5px;
  text-transform: uppercase;
  color: var(--color-white);
}

.newsletter-content h3 span{
  color: var(--color-yellow);
}

.newsletter-content p{
  color: var(--color-white);
  font-size: 18px;
  text-transform: lowercase;
}

.subscribe-form{
  margin-top: 20px;
}

.subscribe-form .form-control {
  padding: 18px 150px 18px 30px;
  border-radius: 50px;
  box-shadow: none;
  border: none;
}

.subscribe-form{
  position: relative;
}

.subscribe-form .theme-btn {
  position: absolute;
  right: -10px;
  top: 5px;
  border-radius: 50px;
}


@media all and (max-width: 991px) {
  .newsletter-wrap{
    padding: 80px 20px;
  }

  .subscribe-form .form-control {
    padding: 18px 135px 18px 20px;
  }
}




/*====================
64. Footer css 
======================*/

.footer-area {
  background: var(--footer-bg);
  position: relative;
  z-index: 1;
}

.ft-bg::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/02.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  opacity: .05;
  z-index: -1;
}

.footer-widget-box {
  margin-bottom: 20px;
}

.footer-widget {
  position: relative;
  z-index: 1;
}

.footer-logo img {
  width: 200px;
  margin-bottom: 15px;
}

.footer-widget-title {
  color: var(--color-white);
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 18px;
  z-index: 1;
}

.footer-widget-title::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 2px;
  background: var(--theme-color);
  border-radius: 50px;
  bottom: 0;
  left: 0;
  z-index: -1;
}

.footer-widget-title::after {
  content: '';
  position: absolute;
  width: 30px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  bottom: 0;
  left: 14px;
  z-index: -1;
}

.footer-list {
  display: flex;
  flex-direction: column;
  gap: .65rem;
}

.footer-list li a {
  position: relative;
  color: var(--color-white);
  transition: var(--transition);
}

.footer-list li a::before{
  content: ".";
  position: absolute;
  left: -3px;
  top: .5px;
  font-size: 50px;
  line-height: 0;
  color: var(--theme-color);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.footer-list li a:hover::before{
  opacity: 1;
  visibility: visible;
}

.footer-list li a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.footer-list li a:hover {
  padding-left: 12px;
  color: var(--theme-color);
}

.footer-widget-box p {
  color: var(--color-white);
  padding-right: 18px;
  margin-bottom: 20px;
}

.footer-contact li {
  position: relative;
  display: flex;
  justify-content: start;
  align-items: center;
  color: var(--footer-text-color);
  font-size: 16px;
  margin-bottom: 8px;
}

.footer-contact li a {
  color: var(--footer-text-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.footer-contact li i {
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 16px;
  margin-right: 11px;
  border-radius: 50px;
  background: var(--theme-color);
  text-align: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: var(--color-white);
}

.footer-download h5{
  color: var(--color-white);
  margin-top: 10px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
}

.footer-download-btn{
  display: flex;
  gap: 10px;
}

.footer-download-btn a{
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 13px 10px 10px;
  background: var(--theme-color);
  color: var(--color-white);
  border-radius: 7px;
  box-shadow: var(--box-shadow);
}

.footer-download-btn a i{
  font-size: 32px;
}

.download-btn-info{
  display: flex;
  flex-direction: column;
}

.download-btn-info span{
  font-weight: 500;
  font-size: 12px;
  line-height: 1;
}

.download-btn-info h6{
  color: var(--color-white);
  font-size: 14px;
  margin-top: 5px;
}

.copyright {
  position: relative;
}

.copyright-wrap{
  padding: 28px 0;
  border-top: 1px solid var(--border-white-color);
}

.copyright .copyright-text {
  color: var(--footer-text-color);
  margin-bottom: 0px;
  font-size: 16px;
}

.copyright .copyright-text a {
  color: var(--theme-color);
  font-weight: 500;
}

.footer-payment span{
  display: block;
  color: var(--color-white);
  margin-bottom: 8px;
}

.footer-payment img{
  width: 50px;
  margin-right: 2px;
}

.footer-social {
  display: flex;
  gap: 15px;
  justify-content: end;
}

.footer-social span{
  line-height: 39px;
  color: var(--color-white);
}

.footer-social a{
  height: 38px;
  width: 38px;
  line-height: 39px;
  text-align: center;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-white);
  transition: var(--transition);
}

.footer-social a:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

/* footer area 2 */
.footer-area2{
  background: var(--theme-bg-light);
}

.footer-area2 .footer-widget-title{
  color: var(--color-dark);
  font-weight: 600;
}

.footer-area2 .footer-widget-title::after{
  background: var(--theme-color);
}

.footer-area2 .footer-widget-box p{
  color: var(--body-text-color);
}

.footer-area2 .footer-contact li{
  margin-bottom: 9px;
}

.footer-area2 .footer-list li a{
  color: var(--body-text-color);
  position: relative;
}
.footer-area2 .footer-list li a:hover{
  color: var(--theme-color);
}

.footer-area2 .footer-contact li,
.footer-area2 .footer-contact li a{
  color: var(--body-text-color);
}

.footer-area2 .footer-contact li i{
  background: var(--theme-color);
}

.footer-area2 .footer-newsletter .form-control{
  border-radius: 12px;
  padding: 16px 70px 16px 18px;
  border: 1px solid var(--border-info-color);
}

.footer-area2 .footer-newsletter .theme-btn{
  border-radius: 10px;
  top: 5px;
  right: 5px;
  padding: 9px 18px
}

.footer-area2 .footer-newsletter .theme-btn i{
  margin-left: 0;
}

.footer-area2 .footer-download h5{
  color: var(--color-dark);
  margin-bottom: 20px;
}

.footer-area2 .footer-download-btn a{
  background: var(--theme-color);
}

.footer-area2 .copyright-wrap{
  border-top: 1px solid var(--border-info-color);
}

.footer-area2 .footer-social span,
.footer-area2 .footer-payment span,
.footer-area2 .copyright .copyright-text{
  color: var(--body-text-color);
}

.footer-area2 .footer-social a{
  background: var(--theme-color);
}

.footer-area2 .footer-top-link h5{
  color: var(--color-dark);
}

.footer-area2 .footer-top-link-info a{
  border-color: var(--border-info-color);
  color: var(--body-text-color);
}

.footer-area2 .footer-top-link-info a:hover{
  color: var(--color-white);
}

@media all and (min-width: 1199px) and (max-width: 1399px) {
  .footer-download-btn a i{
    font-size: 27px;
  }

  .download-btn-info span{
    font-size: 10px;
  }

  .download-btn-info h6{
    font-size: 12px;
  }

  .footer-payment img{
    width: 46px;
  }
}

@media all and (min-width: 992px) and (max-width: 1199px) {
  .footer-payment span,
  .footer-social span{
    display: none;
  }

  .footer-payment img{
    width: 38px;
  }
}

@media all and (max-width: 991px) {
  .footer-widget-box {
    margin-bottom: 50px;
  }

  .footer-payment{
    margin-bottom: 20px;
  }

  .footer-social {
    justify-content: flex-start;
    margin-top: 20px;
  }

  .footer-top-link {
    margin-top: 8px;
  }

  .copyright .copyright-text{
    text-align: start;
  }
}



/*====================
65. Home 2 css 
======================*/

@media all and (max-width: 991px) {
  .home-2 .navbar{
    background: transparent;
  }

  .home-2 .navbar.fixed-top{
    background: var(--color-white);
  }
}



/*====================
66. Home 3 css 
======================*/

.home-3 .hs-1 .hero-slider{
  border-radius: 0;
}

.home-3 .hs-1 .hero-single{
  padding-top: 60px;
  padding-bottom: 60px;
}

.home-3 .hero-single .hero-content .hero-title{
  font-size: 60px;
}



