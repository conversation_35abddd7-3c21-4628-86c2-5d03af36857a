@extends('layouts.admin')

@section('title', 'Banners')

@section('content_header')
    <div class="row mb-2">
        <div class="col-sm-6">
            <h1>Banners</h1>
        </div>
        <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active">Banners</li>
            </ol>
        </div>
    </div>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between">
                <h3 class="card-title">All Banners</h3>
                <a href="{{ route('banners.create') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-lg"></i> Add New Banner
                </a>
            </div>
        </div>
        <div class="card-body">
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Title</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($banners as $banner)
                            <tr>
                                <td>{{ $banner->id }}</td>
                                <td>
                                    <img src="{{ asset($banner->image_link) }}" alt="{{ $banner->name }}" class="img-thumbnail"
                                        style="max-width: 100px;">
                                </td>
                                <td>{{ $banner->name }}</td>
                                <td>{{ $banner->title }}</td>
                                <td>
                                    <span class="badge bg-{{ $banner->status === 'active' ? 'success' : 'danger' }}">
                                        {{ ucfirst($banner->status) }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('banners.show', $banner) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('banners.edit', $banner) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <form action="{{ route('banners.destroy', $banner) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger"
                                            onclick="return confirm('Are you sure you want to delete this banner?')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">No banners found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                {{ $banners->links() }}
            </div>
        </div>
    </div>
@endsection