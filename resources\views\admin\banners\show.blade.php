@extends('layouts.admin')

@section('title', 'Banner Details')

@section('content_header')
    <div class="row mb-2">
        <div class="col-sm-6">
            <h1>Banner Details</h1>
        </div>
        <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('banners.index') }}">Banners</a></li>
                <li class="breadcrumb-item active">Details</li>
            </ol>
        </div>
    </div>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between">
                <h3 class="card-title">Banner: {{ $banner->name }}</h3>
                <div>
                    <a href="{{ route('banners.edit', $banner) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <form action="{{ route('banners.destroy', $banner) }}" method="POST" class="d-inline"
                        onsubmit="return confirm('Are you sure you want to delete this banner?');">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">ID</th>
                            <td>{{ $banner->id }}</td>
                        </tr>
                        <tr>
                            <th>Name</th>
                            <td>{{ $banner->name }}</td>
                        </tr>
                        <tr>
                            <th>Title</th>
                            <td>{{ $banner->title }}</td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ $banner->description ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Shop Now Link</th>
                            <td>
                                @if($banner->shop_now_link)
                                    <a href="{{ $banner->shop_now_link }}" target="_blank">{{ $banner->shop_now_link }}</a>
                                @else
                                    N/A
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <span class="badge bg-{{ $banner->status === 'active' ? 'success' : 'danger' }}">
                                    {{ ucfirst($banner->status) }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ $banner->created_at->format('M d, Y H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>Updated At</th>
                            <td>{{ $banner->updated_at->format('M d, Y H:i:s') }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Banner Image</h5>
                        </div>
                        <div class="card-body text-center">
                            <img src="{{ asset('storage/' . $banner->image_link) }}" alt="{{ $banner->name }}"
                                class="img-fluid" style="max-height: 400px;">
                            <div class="mt-3">
                                <p class="text-muted">Image dimensions: 1920 x 1280 pixels</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <a href="{{ route('banners.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>
@endsection