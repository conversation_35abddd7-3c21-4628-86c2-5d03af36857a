@extends('layouts.admin')

@section('title', 'Add Category')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('categories.index') }}">Categories</a></li>
    <li class="breadcrumb-item active">Add New</li>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Add New Category</h3>
            <div class="card-tools">
                <a href="{{ route('categories.index') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
        </div>
        <div class="card-body">
            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <form action="{{ route('categories.store') }}" method="POST" enctype="multipart/form-data" id="categoryForm">
                @csrf
                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name"
                        value="{{ old('name') }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="slug" class="form-label">Slug (optional)</label>
                    <input type="text" class="form-control @error('slug') is-invalid @enderror" id="slug" name="slug"
                        value="{{ old('slug') }}" placeholder="Leave empty for auto-generation">
                    @error('slug')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="editor @error('description') is-invalid @enderror" id="description"
                        name="description">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="parent_id" class="form-label">Parent Category</label>
                    <select class="form-control @error('parent_id') is-invalid @enderror" id="parent_id" name="parent_id">
                        <option value="">None</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ old('parent_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('parent_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                        <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                    @error('status')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label class="form-label">Category Images</label>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> <strong>Recommended:</strong> Upload 300 x 300 PNG images for
                        best results.
                        Images will be automatically resized to 300x300 pixels while maintaining aspect ratio.
                    </div>
                    <div id="dropzone" class="dropzone"></div>
                    <input type="hidden" name="primary_image" id="primaryImage" value="0">
                    @error('images')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                <button type="submit" class="btn btn-primary">Create Category</button>
            </form>
        </div>
    </div>
@endsection

@section('styles')
    <link href="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone.css" rel="stylesheet" type="text/css" />
@endsection

@push('scripts')
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/36.0.1/classic/ckeditor.js"></script>
    <script>
        class MyUploadAdapter {
            constructor(loader) {
                this.loader = loader;
            }

            upload() {
                return this.loader.file
                    .then(file => new Promise((resolve, reject) => {
                        this._uploadFile(file).then(response => {
                            resolve({
                                default: response.url
                            });
                        }).catch(error => {
                            reject(error);
                        });
                    }));
            }

            _uploadFile(file) {
                const formData = new FormData();
                formData.append('upload', file);
                formData.append('_token', '{{ csrf_token() }}');

                return fetch('{{ route("ckeditor.upload") }}', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .catch(error => {
                        console.error('Error:', error);
                        throw error;
                    });
            }

            abort() {
                // Abort upload implementation
            }
        }

        function MyCustomUploadAdapterPlugin(editor) {
            editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
                return new MyUploadAdapter(loader);
            };
        }

        ClassicEditor
            .create(document.querySelector('.editor'), {
                extraPlugins: [MyCustomUploadAdapterPlugin],
                toolbar: {
                    items: [
                        'undo', 'redo',
                        '|', 'heading',
                        '|', 'bold', 'italic',
                        '|', 'link', 'uploadImage', 'insertTable', 'mediaEmbed',
                        '|', 'bulletedList', 'numberedList',
                        '|', 'outdent', 'indent'
                    ]
                },
                image: {
                    toolbar: [
                        'imageStyle:inline',
                        'imageStyle:block',
                        'imageStyle:side',
                        '|',
                        'toggleImageCaption',
                        'imageTextAlternative'
                    ]
                },
                height: '300px',
                minHeight: '300px'
            })
            .catch(error => {
                console.error(error);
            });
    </script>

    <!-- Dropzone -->
    <script src="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone-min.js"></script>
    <script>
        // Initialize Dropzone
        Dropzone.autoDiscover = false;
        let myDropzone = new Dropzone("#dropzone", {
            url: "{{ route('categories.store') }}",
            autoProcessQueue: false,
            uploadMultiple: true,
            parallelUploads: 5,
            maxFiles: 5,
            maxFilesize: 2,
            acceptedFiles: ".png, .jpg, .jpeg",
            dictDefaultMessage: "Drop files here or click to upload (300x300 PNG recommended)",
            addRemoveLinks: true,
            paramName: "images",
            init: function () {
                var dz = this;
                var form = document.getElementById('categoryForm');

                // Define setPrimaryImage method within the instance
                dz.setPrimaryImage = function (file) {
                    // Remove primary status from all files
                    this.files.forEach(f => {
                        f.isPrimary = false;
                        if (f.previewElement) {
                            f.previewElement.classList.remove('is-primary');
                            const btn = f.previewElement.querySelector('.set-primary-btn');
                            if (btn) btn.textContent = 'Set as Primary';
                        }
                    });

                    // Set new primary file
                    file.isPrimary = true;
                    file.previewElement.classList.add('is-primary');
                    const btn = file.previewElement.querySelector('.set-primary-btn');
                    if (btn) btn.textContent = 'Primary Image';

                    // Update hidden input with the current index of primary image
                    const currentIndex = this.files.indexOf(file);
                    document.getElementById('primaryImage').value = currentIndex;
                };

                // Handle form submission
                form.addEventListener('submit', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    if (dz.getQueuedFiles().length === 0) {
                        alert('Please add at least one image');
                        return;
                    }

                    // Clear any existing error messages
                    clearErrors();

                    dz.processQueue();
                });

                this.on("addedfile", function (file) {
                    // Create "Set as Primary" button
                    const setPrimaryBtn = document.createElement('button');
                    setPrimaryBtn.className = 'btn btn-sm btn-primary set-primary-btn mt-2';
                    setPrimaryBtn.textContent = 'Set as Primary';

                    // Add button to file preview element
                    file.previewElement.appendChild(setPrimaryBtn);

                    // Set first uploaded file as primary by default
                    if (this.files.length === 1) {
                        dz.setPrimaryImage(file);
                    }

                    // Add click event to button
                    setPrimaryBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        dz.setPrimaryImage(file);
                    });
                });

                this.on("removedfile", function (file) {
                    const primaryFile = this.files.find(f => f.isPrimary);
                    const wasRemovingPrimary = file.isPrimary;

                    // Wait for Dropzone to complete the removal
                    setTimeout(() => {
                        // If we removed the primary file
                        if (wasRemovingPrimary && this.files.length > 0) {
                            this.setPrimaryImage(this.files[0]);
                        }
                        // If we removed a non-primary file
                        else if (primaryFile && primaryFile.isPrimary) {
                            // Re-set the primary file to update indices
                            this.setPrimaryImage(primaryFile);
                        }
                    }, 0);
                });

                this.on("sending", function (file, xhr, formData) {
                    // Show loading state
                    document.querySelector('button[type="submit"]').disabled = true;

                    // Append all form data
                    var formElements = form.elements;
                    for (var i = 0; i < formElements.length; i++) {
                        if (formElements[i].type !== 'file') {
                            formData.append(formElements[i].name, formElements[i].value);
                        }
                    }
                });

                this.on("success", function (file, response) {
                    if (response.success) {
                        window.location.href = response.redirect;
                    }
                });

                this.on("error", function (file, errorMessage) {
                    // Re-enable submit button
                    document.querySelector('button[type="submit"]').disabled = false;

                    if (typeof errorMessage === 'object' && errorMessage.errors) {
                        // Handle validation errors
                        displayErrors(errorMessage.errors);

                        // Keep the files in dropzone
                        file.status = Dropzone.QUEUED;
                        this.files.push(file);
                    } else {
                        console.error('Upload error:', errorMessage);
                        alert('Error uploading files. Please try again.');
                    }
                });

                this.on("complete", function (file) {
                    document.querySelector('button[type="submit"]').disabled = false;
                });
            }
        });

        // Helper functions for error handling
        function clearErrors() {
            // Remove all error messages
            document.querySelectorAll('.invalid-feedback').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });

            // Remove invalid classes
            document.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
        }

        function displayErrors(errors) {
            for (let field in errors) {
                const input = document.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('is-invalid');

                    // Find or create error div
                    let errorDiv = input.nextElementSibling;
                    if (!errorDiv || !errorDiv.classList.contains('invalid-feedback')) {
                        errorDiv = document.createElement('div');
                        errorDiv.classList.add('invalid-feedback');
                        input.parentNode.insertBefore(errorDiv, input.nextSibling);
                    }

                    errorDiv.style.display = 'block';
                    errorDiv.textContent = errors[field][0];
                }
            }
        }

        // Add some CSS for error states
        const style = document.createElement('style');
        style.textContent = `
                                                                                                                        .invalid-feedback {
                                                                                                                            display: none;
                                                                                                                            color: #dc3545;
                                                                                                                            font-size: 80%;
                                                                                                                            margin-top: 0.25rem;
                                                                                                                        }
                                                                                                                        .is-invalid {
                                                                                                                            border-color: #dc3545 !important;
                                                                                                                        }
                                                                                                                        .is-invalid ~ .invalid-feedback {
                                                                                                                            display: block;
                                                                                                                        }
                                                                                                                    `;
        document.head.appendChild(style);
    </script>
    <style>
        .dropzone {
            border: 2px dashed #0087F7;
            border-radius: 5px;
            background: white;
            min-height: 150px;
            padding: 20px;
            margin-bottom: 20px;
        }

        /* Style for primary image preview */
        .dropzone .dz-preview.is-primary {
            border: 2px solid #198754;
            border-radius: 5px;
            padding: 5px;
        }

        .dropzone .dz-preview .set-primary-btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
@endpush