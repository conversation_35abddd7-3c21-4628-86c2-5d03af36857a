@extends('layouts.admin')

@section('title', 'Edit Listing')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('listings.index') }}">Listings</a></li>
    <li class="breadcrumb-item active">Edit</li>
@endsection

@section('styles')
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Edit Listing</h3>
        </div>
        
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show m-3" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <form action="{{ route('listings.update', $listing) }}" method="POST">
            @csrf
            @method('PUT')
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                id="name" name="name" value="{{ old('name', $listing->name) }}">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="slug" class="form-label">Slug</label>
                            <input type="text" class="form-control @error('slug') is-invalid @enderror" id="slug"
                                name="slug" value="{{ old('slug', $listing->slug) }}">
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description"
                                name="description" rows="5">{{ old('description', $listing->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="active" {{ old('status', $listing->status) == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $listing->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Products</label>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="productsTable">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th style="width: 100px">Position</th>
                                            <th style="width: 80px">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($listing->products as $product)
                                            <tr>
                                                <td>
                                                    <input type="hidden" name="products[]" value="{{ $product->id }}">
                                                    {{ $product->name }}
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control form-control-sm" 
                                                        name="product_positions[]" 
                                                        value="{{ $product->pivot->position }}" min="0">
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger btn-sm remove-product">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="mb-3">
                            <button type="button" class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                Add Product
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button type="submit" class="btn btn-primary">Update Listing</button>
                <a href="{{ route('listings.index') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProductModalLabel">Add Products</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="productSelect" class="form-label">Select Product</label>
                        <select class="form-control select2" id="productSelect" aria-label="Select Product">
                            <option value="">Select Product</option>
                            @foreach($products as $product)
                                @unless($selectedProducts->contains($product->id))
                                    <option value="{{ $product->id }}" data-name="{{ $product->name }}">
                                        {{ $product->name }}
                                    </option>
                                @endunless
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="addSelectedProduct">Add Product</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <!-- jQuery (required for Select2) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
    $(document).ready(function() {
        // Initialize Select2 first
        const $select2 = $('#productSelect').select2({
            theme: 'bootstrap-5',
            dropdownParent: $('#addProductModal'),
            width: '100%'
        });

        // Handle modal events
        $('#addProductModal').on('shown.bs.modal', function () {
            // Focus on the Select2 input
            setTimeout(function() {
                $select2.select2('focus');
            }, 100);
        });

        $('#addProductModal').on('hide.bs.modal', function () {
            $select2.select2('close');
        });

        // Add product to table
        $('#addSelectedProduct').click(function() {
            const productId = $select2.val();
            const productName = $select2.find('option:selected').data('name');
            
            if (productId) {
                const newRow = `
                    <tr>
                        <td>
                            <input type="hidden" name="products[]" value="${productId}">
                            ${productName}
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm" 
                                name="product_positions[]" value="0" min="0">
                        </td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm remove-product" aria-label="Remove ${productName}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                
                $('#productsTable tbody').append(newRow);
                $select2.find(`option[value="${productId}"]`).remove();
                $select2.val('').trigger('change');
                $('#addProductModal').modal('hide');
            }
        });

        // Remove product from table
        $(document).on('click', '.remove-product', function() {
            const row = $(this).closest('tr');
            const productId = row.find('input[name="products[]"]').val();
            const productName = row.find('td:first').text().trim();
            
            $select2.append(
                `<option value="${productId}" data-name="${productName}">${productName}</option>`
            );
            
            row.remove();
        });
    });
    </script>
@endpush
