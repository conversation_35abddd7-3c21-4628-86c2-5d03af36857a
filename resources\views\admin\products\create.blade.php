@extends('layouts.admin')

@section('title', 'Add Product')

@section('styles')
    <link rel="stylesheet" href="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone.css" />
@endsection

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
    <li class="breadcrumb-item active">Add New</li>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Add New Product</h3>
        </div>
        <div class="card-body">
            <form action="{{ route('products.store') }}" method="POST" enctype="multipart/form-data" id="productForm">
                @csrf

                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name"
                        value="{{ old('name') }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="slug" class="form-label">Slug (optional)</label>
                    <input type="text" class="form-control @error('slug') is-invalid @enderror" id="slug" name="slug"
                        value="{{ old('slug') }}" placeholder="Leave empty for auto-generation">
                    @error('slug')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="editor @error('description') is-invalid @enderror" id="description"
                        name="description">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="youtube_video_url" class="form-label">YouTube Video URL (optional)</label>
                    <input type="url" class="form-control @error('youtube_video_url') is-invalid @enderror"
                        id="youtube_video_url" name="youtube_video_url" value="{{ old('youtube_video_url') }}"
                        placeholder="https://www.youtube.com/watch?v=...">
                    <div class="form-text">Enter the full YouTube video URL (e.g., https://www.youtube.com/watch?v=abcdefg)
                    </div>
                    @error('youtube_video_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="price" class="form-label">Price</label>
                    <div class="input-group">
                        <span class="input-group-text">₹</span>
                        <input type="number" class="form-control @error('price') is-invalid @enderror" id="price"
                            name="price" value="{{ old('price') }}" step="0.01" min="0" required>
                    </div>
                    @error('price')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="categories" class="form-label">Categories</label>
                    <select class="form-control select2 @error('categories') is-invalid @enderror" id="categories"
                        name="categories[]" multiple required>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ (old('categories') && in_array($category->id, old('categories'))) ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('categories')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label class="form-label">Product Images</label>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> <strong>Recommended:</strong> Upload 500 x 500 PNG images for
                        best results.
                        Images will be automatically resized to 500x500 pixels while maintaining aspect ratio.
                    </div>
                    <div id="dropzone" class="dropzone"></div>
                    <input type="hidden" name="primary_image" id="primaryImage" value="0">
                    @error('images')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Create Product</button>
                    <a href="{{ route('products.index') }}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/36.0.1/classic/ckeditor.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Dropzone -->
    <script src="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone-min.js"></script>

    <script>
        // Initialize CKEditor
        ClassicEditor
            .create(document.querySelector('.editor'), {
                toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
            })
            .catch(error => {
                console.error(error);
            });

        // Initialize Select2
        $(document).ready(function () {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: 'Select categories',
                selectionCssClass: 'select2--large',
                dropdownCssClass: 'select2--large',
            });
        });

        // Initialize Dropzone
        Dropzone.autoDiscover = false;
        let myDropzone = new Dropzone("#dropzone", {
            url: "{{ route('products.store') }}",
            autoProcessQueue: false,
            uploadMultiple: true,
            parallelUploads: 5,
            maxFiles: 5,
            maxFilesize: 2,
            acceptedFiles: "image/*",
            addRemoveLinks: true,
            paramName: "images",

            init: function () {
                var dz = this;
                var form = document.getElementById('productForm');

                // Define setPrimaryImage method within the instance
                dz.setPrimaryImage = function (file) {
                    // Remove primary status from all files
                    this.files.forEach(f => {
                        f.isPrimary = false;
                        if (f.previewElement) {
                            f.previewElement.classList.remove('is-primary');
                            const btn = f.previewElement.querySelector('.set-primary-btn');
                            if (btn) btn.textContent = 'Set as Primary';
                        }
                    });

                    // Set new primary file
                    file.isPrimary = true;
                    file.previewElement.classList.add('is-primary');
                    const btn = file.previewElement.querySelector('.set-primary-btn');
                    if (btn) btn.textContent = 'Primary Image';

                    // Update hidden input with the current index of primary image
                    document.getElementById('primaryImage').value = this.files.indexOf(file);
                };

                // Add "Set as Primary" button when file is added
                this.on("addedfile", function (file) {
                    // Create "Set as Primary" button
                    const setPrimaryBtn = document.createElement('button');
                    setPrimaryBtn.className = 'btn btn-sm btn-primary set-primary-btn mt-2';
                    setPrimaryBtn.textContent = 'Set as Primary';

                    // Add button to file preview element
                    file.previewElement.appendChild(setPrimaryBtn);

                    // Set first uploaded file as primary by default
                    if (this.files.length === 1) {
                        dz.setPrimaryImage(file);
                    }

                    // Add click event to button
                    setPrimaryBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        dz.setPrimaryImage(file);
                    });
                });

                // Handle form submission
                form.addEventListener('submit', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    if (dz.getQueuedFiles().length === 0) {
                        alert('Please add at least one image');
                        return;
                    }

                    dz.processQueue();
                });

                this.on("sendingmultiple", function (files, xhr, formData) {
                    // Append form data
                    let formElements = form.elements;
                    for (let i = 0; i < formElements.length; i++) {
                        if (formElements[i].name) {
                            if (formElements[i].type === 'select-multiple') {
                                let selectedOptions = Array.from(formElements[i].selectedOptions);
                                selectedOptions.forEach(option => {
                                    formData.append(formElements[i].name, option.value);
                                });
                            } else {
                                formData.append(formElements[i].name, formElements[i].value);
                            }
                        }
                    }
                });

                this.on("successmultiple", function (files, response) {
                    if (response.success) {
                        window.location.href = response.redirect;
                    }
                });

                this.on("errormultiple", function (files, response) {
                    if (response.errors) {
                        Object.keys(response.errors).forEach(key => {
                            let input = document.querySelector(`[name="${key}"]`);
                            if (input) {
                                input.classList.add('is-invalid');
                                let feedback = input.parentElement.querySelector('.invalid-feedback');
                                if (!feedback) {
                                    feedback = document.createElement('div');
                                    feedback.classList.add('invalid-feedback');
                                    input.parentElement.appendChild(feedback);
                                }
                                feedback.textContent = response.errors[key][0];
                            }
                        });
                    }
                });
            }
        });

        // Slug generation
        document.getElementById('name').addEventListener('blur', function () {
            if (document.getElementById('slug').value === '') {
                let slug = this.value
                    .toLowerCase()
                    .replace(/[^a-z0-9-]/g, '-')
                    .replace(/-+/g, '-')
                    .replace(/^-|-$/g, '');
                document.getElementById('slug').value = slug;
            }
        });
    </script>

    <style>
        .primary-image {
            border: 2px solid #007bff !important;
        }

        .dropzone {
            border: 2px dashed #0087F7;
            border-radius: 5px;
            background: white;
        }

        /* Updated Select2 styles */
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            border: 1px solid #dee2e6;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
            padding: 0;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            margin-top: 0;
            margin-right: 0.375rem;
            margin-bottom: 0.375rem;
        }

        .select2--large {
            font-size: 1rem !important;
        }
    </style>
@endpush