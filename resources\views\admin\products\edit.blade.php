@extends('layouts.admin')

@section('title', 'Edit Product')

@section('styles')
    <link rel="stylesheet" href="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone.css" />
@endsection

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
    <li class="breadcrumb-item active">Edit Product</li>
@endsection

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Edit Product</h3>
        </div>
        <div class="card-body">
            <form action="{{ route('products.update', $product) }}" method="POST" enctype="multipart/form-data"
                id="productForm">
                @csrf
                @method('PUT')

                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name"
                        value="{{ old('name', $product->name) }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="slug" class="form-label">Slug</label>
                    <input type="text" class="form-control @error('slug') is-invalid @enderror" id="slug" name="slug"
                        value="{{ old('slug', $product->slug) }}">
                    @error('slug')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control editor @error('description') is-invalid @enderror" id="description"
                        name="description">{{ old('description', $product->description) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="youtube_video_url" class="form-label">YouTube Video URL (optional)</label>
                    <input type="url" class="form-control @error('youtube_video_url') is-invalid @enderror" id="youtube_video_url"
                        name="youtube_video_url" value="{{ old('youtube_video_url', $product->youtube_video_url) }}" placeholder="https://www.youtube.com/watch?v=...">
                    <div class="form-text">Enter the full YouTube video URL (e.g., https://www.youtube.com/watch?v=abcdefg)</div>
                    @error('youtube_video_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="price" class="form-label">Price</label>
                    <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" id="price"
                        name="price" value="{{ old('price', $product->price) }}" required>
                    @error('price')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="categories" class="form-label">Categories</label>
                    <select class="form-control select2 @error('categories') is-invalid @enderror" id="categories"
                        name="categories[]" multiple required>
                        @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ (old('categories') && in_array($category->id, old('categories'))) ||
                            (!old('categories') && $product->categories->contains($category->id)) ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                        @endforeach
                    </select>
                    @error('categories')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label class="form-label">Product Images</label>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> <strong>Recommended:</strong> Upload 500 x 500 PNG images for best results.
                        Images will be automatically resized to 500x500 pixels while maintaining aspect ratio.
                    </div>
                    <div id="dropzone" class="dropzone"></div>
                    <input type="hidden" name="primary_image" id="primaryImage" value="{{ $product->primary_image_index }}">
                    @error('images')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Update Product</button>
                    <a href="{{ route('products.index') }}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/36.0.1/classic/ckeditor.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Dropzone -->
    <script src="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone-min.js"></script>

    <script>
        // Initialize CKEditor
        ClassicEditor
            .create(document.querySelector('.editor'), {
                toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
            })
            .catch(error => {
                console.error(error);
            });

        // Initialize Select2
        $(document).ready(function () {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: 'Select categories',
                selectionCssClass: 'select2--large',
                dropdownCssClass: 'select2--large',
            });
        });

        // Initialize Dropzone
        Dropzone.autoDiscover = false;
        let myDropzone = new Dropzone("#dropzone", {
            url: "{{ route('products.update', $product) }}",
            autoProcessQueue: false,
            uploadMultiple: true,
            parallelUploads: 5,
            maxFiles: 5,
            maxFilesize: 2,
            acceptedFiles: "image/*",
            addRemoveLinks: false, // Changed to false to remove Dropzone's default remove link
            paramName: "images",
            dictDefaultMessage: "Drop files here or click to upload", // Custom message instead of label
            autoQueue: true, // Ensure files are automatically queued

            init: function () {
                var dz = this;
                var form = document.getElementById('productForm');
                var deletedImages = [];

                // Add hidden input for deleted images if not exists
                if (!document.getElementById('deletedImages')) {
                    const deletedImagesInput = document.createElement('input');
                    deletedImagesInput.type = 'hidden';
                    deletedImagesInput.name = 'remove_images';
                    deletedImagesInput.id = 'deletedImages';
                    deletedImagesInput.value = '';
                    form.appendChild(deletedImagesInput);
                }

                // Load existing images
                @if($product->images)
                    @foreach($product->images as $image)
                        (() => {
                            const mockFile = {
                                name: "{{ $image->original_name }}",
                                size: {{ $image->size }},
                                imageId: {{ $image->id }}
                            };
                            dz.displayExistingFile(mockFile, "{{ Storage::url($image->path) }}");

                            const container = mockFile.previewElement;
                            container.classList.add('existing-image');
                            container.dataset.imageId = {{ $image->id }};

                            // Add custom buttons container
                            const buttonContainer = document.createElement('div');
                            buttonContainer.className = 'mt-2 d-flex gap-2 justify-content-center';

                            // Add delete button
                            const deleteBtn = document.createElement('button');
                            deleteBtn.type = 'button';
                            deleteBtn.className = 'btn btn-sm btn-danger delete-image';
                            deleteBtn.textContent = 'Remove'; // Simple text, no icon

                            // Add set primary button
                            const setPrimaryBtn = document.createElement('button');
                            setPrimaryBtn.type = 'button';
                            setPrimaryBtn.className = 'btn btn-sm btn-primary set-primary-existing';
                            setPrimaryBtn.innerHTML = @json($image->is_primary ? 'Primary Image' : 'Set as Primary');
                            setPrimaryBtn.disabled = {{ $image->is_primary ? 'true' : 'false' }};

                            buttonContainer.appendChild(deleteBtn);
                            buttonContainer.appendChild(setPrimaryBtn);
                            container.appendChild(buttonContainer);

                            @if($image->is_primary)
                                container.classList.add('is-primary');
                                document.getElementById('primaryImage').value = {{ $image->id }};
                            @endif

                            // Add event listeners
                            deleteBtn.addEventListener('click', function() {
                                const imageId = container.dataset.imageId;
                                deletedImages.push(imageId);
                                document.getElementById('deletedImages').value = deletedImages.join(',');
                                dz.removeFile(mockFile);
                            });

                            setPrimaryBtn.addEventListener('click', function() {
                                setPrimaryExisting(container);
                            });
                        })();
                    @endforeach
                @endif

                // Add custom buttons for new files
                this.on("addedfile", function(file) {
                    const buttonContainer = document.createElement('div');
                    buttonContainer.className = 'mt-2 d-flex gap-2 justify-content-center';

                    // Add delete button
                    const deleteBtn = document.createElement('button');
                    deleteBtn.type = 'button';
                    deleteBtn.className = 'btn btn-sm btn-danger';
                    deleteBtn.textContent = 'Remove'; // Simple text, no icon
                    deleteBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.removeFile(file);
                    });

                    // Add set primary button
                    const setPrimaryBtn = document.createElement('button');
                    setPrimaryBtn.type = 'button';
                    setPrimaryBtn.className = 'btn btn-sm btn-primary set-primary-btn';
                    setPrimaryBtn.innerHTML = 'Set as Primary';
                    setPrimaryBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.setPrimaryImage(file);
                    });

                    buttonContainer.appendChild(deleteBtn);
                    buttonContainer.appendChild(setPrimaryBtn);
                    file.previewElement.appendChild(buttonContainer);
                });

                // Function to set primary image for existing images
                function setPrimaryExisting(container) {
                    // Remove primary status from all existing images
                    document.querySelectorAll('.existing-image').forEach(img => {
                        img.classList.remove('is-primary');
                        const btn = img.querySelector('.set-primary-existing');
                        if (btn) {
                            btn.textContent = 'Set as Primary';
                            btn.disabled = false;
                        }
                    });

                    // Set new primary
                    container.classList.add('is-primary');
                    const btn = container.querySelector('.set-primary-existing');
                    if (btn) {
                        btn.textContent = 'Primary Image';
                        btn.disabled = true;
                    }

                    // Update hidden input
                    document.getElementById('primaryImage').value = container.dataset.imageId;

                    // Remove primary status from dropzone files
                    if (myDropzone) {
                        myDropzone.files.forEach(file => {
                            if (file.previewElement) {
                                file.previewElement.classList.remove('is-primary');
                                const dzBtn = file.previewElement.querySelector('.set-primary-btn');
                                if (dzBtn) dzBtn.textContent = 'Set as Primary';
                            }
                        });
                    }
                }

                // Define setPrimaryImage method for new files
                this.setPrimaryImage = function(file) {
                    // Remove primary status from all dropzone files
                    this.files.forEach(f => {
                        f.isPrimary = false;
                        if (f.previewElement) {
                            f.previewElement.classList.remove('is-primary');
                            const btn = f.previewElement.querySelector('.set-primary-btn');
                            if (btn) btn.textContent = 'Set as Primary';
                        }
                    });

                    // Remove primary status from existing images
                    document.querySelectorAll('.existing-image').forEach(img => {
                        img.classList.remove('is-primary');
                        const btn = img.querySelector('.set-primary-existing');
                        if (btn) {
                            btn.textContent = 'Set as Primary';
                            btn.disabled = false;
                        }
                    });

                    // Set new primary
                    file.isPrimary = true;
                    file.previewElement.classList.add('is-primary');
                    const btn = file.previewElement.querySelector('.set-primary-btn');
                    if (btn) btn.textContent = 'Primary Image';

                    // Update hidden input with 'new_' prefix for new files
                    document.getElementById('primaryImage').value = 'new_' + this.files.indexOf(file);
                };

                // Handle form submission
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Check if there are any files in the dropzone
                    if (dz.files.length > 0) {
                        // Make sure all files are queued
                        dz.files.forEach(file => {
                            if (!file.upload.uuid) {
                                dz.enqueueFile(file);
                            }
                        });

                        // Ensure primary_image is set
                        const primaryImageInput = document.getElementById('primaryImage');
                        if (!primaryImageInput.value || primaryImageInput.value === '') {
                            // If no primary image is set, set the first file as primary
                            if (dz.files.length > 0) {
                                // Check if there are existing images
                                const existingImages = document.querySelectorAll('.existing-image');
                                if (existingImages.length > 0) {
                                    // Set the first existing image as primary
                                    primaryImageInput.value = existingImages[0].dataset.imageId;
                                } else {
                                    // Set the first new image as primary
                                    primaryImageInput.value = 'new_0';
                                }
                                console.log('Auto-set primary image to: ' + primaryImageInput.value);
                            }
                        }

                        console.log('Processing queue with ' + dz.getQueuedFiles().length + ' files');
                        console.log('Primary image value: ' + primaryImageInput.value);
                        dz.processQueue();
                    } else {
                        console.log('No files to upload, submitting form directly');
                        form.submit();
                    }
                });

                this.on("sendingmultiple", function(files, xhr, formData) {
                    // Append all form data
                    let formElements = form.elements;
                    for (let i = 0; i < formElements.length; i++) {
                        if (formElements[i].name) {
                            formData.append(formElements[i].name, formElements[i].value);
                        }
                    }

                    // Explicitly ensure primary_image is included
                    const primaryImageValue = document.getElementById('primaryImage').value;
                    if (primaryImageValue) {
                        // Remove any existing primary_image entries to avoid duplicates
                        if (formData.has('primary_image')) {
                            formData.delete('primary_image');
                        }
                        formData.append('primary_image', primaryImageValue);
                        console.log('Added primary_image to form data:', primaryImageValue);
                    } else {
                        console.warn('No primary image value found!');
                        // Set a default if none exists
                        formData.append('primary_image', 'new_0');
                    }

                    // Log all form data for debugging
                    console.log('Form data being sent:');
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ': ' + pair[1]);
                    }
                });

                this.on("successmultiple", function(files, response) {
                    if (response.success && response.redirect) {
                        window.location.href = response.redirect;
                    }
                });

                this.on("errormultiple", function(files, response) {
                    console.error('Upload error:', response);
                    alert('Error uploading images. Please check the console for details.');
                });

                // Add additional error handling
                this.on("error", function(file, errorMessage, xhr) {
                    console.error('Dropzone error:', errorMessage);
                    if (xhr) {
                        console.error('Server response:', xhr.responseText);
                    }
                });
            }
        });

        // Slug generation
        document.getElementById('name').addEventListener('blur', function () {
            if (document.getElementById('slug').value === '') {
                let slug = this.value
                    .toLowerCase()
                    .replace(/[^a-z0-9-]/g, '-')
                    .replace(/-+/g, '-')
                    .replace(/^-|-$/g, '');
                document.getElementById('slug').value = slug;
            }
        });
    </script>

    <style>
        .dropzone {
            border: 2px dashed #0087F7;
            border-radius: 5px;
            background: white;
            min-height: 150px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .dropzone .dz-preview.is-primary {
            border: 2px solid #198754;
            border-radius: 5px;
            padding: 5px;
        }

        .dropzone .dz-preview .set-primary-btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        #existingImages {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .existing-image {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
        }

        .existing-image.is-primary {
            border: 2px solid #198754;
        }

        .dropzone .dz-preview .dz-details {
            padding: 1em;
        }

        /* Button container styling */
        .mt-2.d-flex.gap-2.justify-content-center {
            margin-top: 0.5rem;
        }

        /* Button styling */
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 0.2rem;
        }

        /* Image thumbnail styling */
        .img-thumbnail {
            max-width: 200px;
            height: auto;
        }

        .dropzone .dz-preview .dz-image {
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .dropzone .dz-preview .dz-image img {
            max-width: 100%;
            height: auto;
        }

        /* Updated Select2 styles */
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            border: 1px solid #dee2e6;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
            padding: 0;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            margin-top: 0;
            margin-right: 0.375rem;
            margin-bottom: 0.375rem;
        }

        .select2--large {
            font-size: 1rem !important;
        }
    </style>
@endpush














