@extends('layouts.admin')

@section('title', 'Create Setting')

@section('breadcrumbs')
<li class="breadcrumb-item"><a href="{{ route('settings.index') }}">Settings</a></li>
<li class="breadcrumb-item active">Create</li>
@endsection

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Create New Setting</h3>
            </div>
            <div class="card-body">
                <form action="{{ route('settings.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="mb-3">
                        <label for="label" class="form-label">Label <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('label') is-invalid @enderror" id="label" name="label" value="{{ old('label') }}" required>
                        @error('label')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">A human-readable name for this setting.</small>
                    </div>

                    <div class="mb-3">
                        <label for="key" class="form-label">Key <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('key') is-invalid @enderror" id="key" name="key" value="{{ old('key') }}" required>
                        @error('key')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">A unique identifier for this setting (e.g., site_title, contact_email).</small>
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                        <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                            <option value="">Select Type</option>
                            @foreach($types as $typeKey => $typeName)
                                <option value="{{ $typeKey }}" {{ old('type') == $typeKey ? 'selected' : '' }}>{{ $typeName }}</option>
                            @endforeach
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">The type of setting determines how it will be displayed and edited.</small>
                    </div>

                    <!-- Hidden input for the actual value -->
                    <input type="hidden" id="actual-value" name="value" value="{{ old('value') }}">

                    <div id="valueContainer" class="mb-3">
                        <label for="value" class="form-label">Value</label>
                        
                        <!-- Text input -->
                        <div id="text-input" class="value-input">
                            <input type="text" class="form-control @error('value') is-invalid @enderror" id="text-value"
                                value="{{ old('value') }}">
                        </div>

                        <!-- Textarea -->
                        <div id="textarea-input" class="value-input d-none">
                            <textarea class="form-control @error('value') is-invalid @enderror" id="textarea-value"
                                rows="4">{{ old('value') }}</textarea>
                        </div>

                        <!-- URL input -->
                        <div id="url-input" class="value-input d-none">
                            <input type="text" class="form-control @error('value') is-invalid @enderror" id="url-value"
                                value="{{ old('value') }}">
                            <small class="text-muted">Enter a valid URL (e.g., https://example.com)</small>
                        </div>

                        <!-- Image input -->
                        <div id="image-input" class="value-input d-none">
                            <input type="file" class="form-control @error('value') is-invalid @enderror" id="image-value"
                                name="image_upload" accept="image/*">
                            <small class="text-muted">Upload images (JPG, PNG, GIF, etc.)</small>
                        </div>

                        @error('value')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('settings.index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create Setting</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Instructions</h3>
            </div>
            <div class="card-body">
                <p>Settings are stored as key-value pairs that can be used throughout the application.</p>
                <p>To use a setting in your code, you can use the <code>setting('key')</code> helper function.</p>
                <p>Example:</p>
                <pre><code>$siteTitle = setting('site_title', 'Default Title');</code></pre>
                <p>The second parameter is the default value if the setting doesn't exist.</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-generate key from label
            document.getElementById('label').addEventListener('input', function() {
                const label = this.value;
                const key = label.toLowerCase()
                    .replace(/\s+/g, '_')
                    .replace(/[^a-z0-9_]/g, '');
                document.getElementById('key').value = key;
            });

            // Handle different field types
            const typeSelect = document.getElementById('type');
            const valueInputs = document.querySelectorAll('.value-input');
            const actualValueInput = document.getElementById('actual-value');
            
            // Input elements
            const textInput = document.getElementById('text-value');
            const textareaInput = document.getElementById('textarea-value');
            const urlInput = document.getElementById('url-value');
            const imageInput = document.getElementById('image-value');
            
            // Function to update the hidden value input
            function updateActualValue() {
                const selectedType = typeSelect.value;
                
                if (selectedType === 'text') {
                    actualValueInput.value = textInput.value;
                } else if (selectedType === 'textarea') {
                    actualValueInput.value = textareaInput.value;
                } else if (selectedType === 'url') {
                    actualValueInput.value = urlInput.value;
                }
                // For image type, we don't update the hidden input as it's handled by the file upload
            }
            
            // Add input event listeners to update the hidden value
            textInput.addEventListener('input', updateActualValue);
            textareaInput.addEventListener('input', updateActualValue);
            urlInput.addEventListener('input', updateActualValue);

            // Show/hide value inputs based on selected type
            typeSelect.addEventListener('change', function() {
                const selectedType = this.value;
                console.log('Type changed to:', selectedType);
                
                // Hide all inputs first
                valueInputs.forEach(input => input.classList.add('d-none'));
                
                // Show the appropriate input based on the selected type
                if (selectedType === 'text') {
                    document.getElementById('text-input').classList.remove('d-none');
                } else if (selectedType === 'textarea') {
                    document.getElementById('textarea-input').classList.remove('d-none');
                } else if (selectedType === 'url') {
                    document.getElementById('url-input').classList.remove('d-none');
                } else if (selectedType === 'image') {
                    document.getElementById('image-input').classList.remove('d-none');
                } else {
                    // Default to text input
                    document.getElementById('text-input').classList.remove('d-none');
                }
            });
            
            // Initialize with the selected type or default to text
            const initialType = typeSelect.value || 'text';
            
            // Hide all inputs first
            valueInputs.forEach(input => input.classList.add('d-none'));
            
            // Show the appropriate input based on the initial type
            if (initialType === 'text') {
                document.getElementById('text-input').classList.remove('d-none');
            } else if (initialType === 'textarea') {
                document.getElementById('textarea-input').classList.remove('d-none');
            } else if (initialType === 'url') {
                document.getElementById('url-input').classList.remove('d-none');
            } else if (initialType === 'image') {
                document.getElementById('image-input').classList.remove('d-none');
            } else {
                // Default to text input
                document.getElementById('text-input').classList.remove('d-none');
            }
            
            // Add form submission handler
            const form = document.querySelector('form');
            form.addEventListener('submit', function(event) {
                // Update the hidden value input with the current visible input's value
                updateActualValue();
            });
        });
    </script>
@endpush
