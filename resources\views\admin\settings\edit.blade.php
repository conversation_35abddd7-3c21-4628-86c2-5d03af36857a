@extends('layouts.admin')

@section('title', 'Edit Setting')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('settings.index') }}">Settings</a></li>
    <li class="breadcrumb-item active">Edit</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Setting</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('settings.update', $setting->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="label" class="form-label">Label <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('label') is-invalid @enderror" id="label"
                                name="label" value="{{ old('label', $setting->label) }}" required>
                            @error('label')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">A human-readable name for this setting.</small>
                        </div>

                        <div class="mb-3">
                            <label for="key" class="form-label">Key <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('key') is-invalid @enderror" id="key" name="key"
                                value="{{ old('key', $setting->key) }}" required readonly>
                            @error('key')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Unique identifier for this setting (cannot be changed).</small>
                        </div>

                        <div class="mb-3">
                            <label for="type" class="form-label">Type <span class="text-danger">*</span></label>

                            <!-- Actual select, but readonly (visually) -->
                            <select class="form-select" id="type" disabled>
                                <option value="">Select Type</option>
                                @foreach($types as $typeKey => $typeName)
                                    <option value="{{ $typeKey }}" {{ (old('type', $setting->type) == $typeKey) ? 'selected' : '' }}>
                                        {{ $typeName }}
                                    </option>
                                @endforeach
                            </select>

                            <!-- Hidden input to actually submit the value -->
                            <input type="hidden" name="type" value="{{ old('type', $setting->type) }}">

                            <small class="text-muted">Type cannot be changed after creation.</small>
                        </div>

                        <!-- Hidden input for the actual value -->
                        <input type="hidden" id="actual-value" name="value" value="{{ old('value', $setting->value) }}">

                        <div id="valueContainer" class="mb-3">
                            <label for="value" class="form-label">Value</label>

                            <!-- Text input -->
                            <div id="text-input" class="value-input d-none">
                                <input type="text" class="form-control @error('value') is-invalid @enderror" id="text-value"
                                    value="{{ old('value', $setting->value) }}">
                            </div>

                            <!-- Textarea -->
                            <div id="textarea-input" class="value-input d-none">
                                <textarea class="form-control @error('value') is-invalid @enderror" id="textarea-value"
                                    rows="4">{{ old('value', $setting->value) }}</textarea>
                            </div>

                            <!-- URL input -->
                            <div id="url-input" class="value-input d-none">
                                <input type="text" class="form-control @error('value') is-invalid @enderror" id="url-value"
                                    value="{{ old('value', $setting->value) }}">
                                <small class="text-muted">Enter a valid URL (e.g., https://example.com)</small>
                            </div>

                            <!-- Image input -->
                            <div id="image-input" class="value-input d-none">
                                @if($setting->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $setting->value) }}" alt="Current Image"
                                            class="img-thumbnail" style="max-height: 150px;">
                                    </div>
                                @endif
                                <input type="file" class="form-control @error('value') is-invalid @enderror"
                                    id="image-value" name="image_upload" accept="image/*">
                                <small class="text-muted">Upload a new image (JPG, PNG, etc.)</small>
                            </div>

                            @error('value')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('settings.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Update Setting</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Instructions</h3>
                </div>
                <div class="card-body">
                    <p>Edit the value of the setting as needed. The key and type are locked once created.</p>
                    <p>Use <code>setting('key')</code> in your application code to retrieve the setting.</p>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const typeSelect = document.getElementById('type');
            const valueInputs = document.querySelectorAll('.value-input');
            const actualValueInput = document.getElementById('actual-value');

            // Input elements
            const textInput = document.getElementById('text-value');
            const textareaInput = document.getElementById('textarea-value');
            const urlInput = document.getElementById('url-value');
            const imageInput = document.getElementById('image-value');

            // Update hidden field
            function updateActualValue() {
                const selectedType = typeSelect.value;
                if (selectedType === 'text') {
                    actualValueInput.value = textInput.value;
                } else if (selectedType === 'textarea') {
                    actualValueInput.value = textareaInput.value;
                } else if (selectedType === 'url') {
                    actualValueInput.value = urlInput.value;
                }
                // For image, handled by file upload
            }

            textInput.addEventListener('input', updateActualValue);
            textareaInput.addEventListener('input', updateActualValue);
            urlInput.addEventListener('input', updateActualValue);

            // Show relevant input
            function showRelevantInput(type) {
                valueInputs.forEach(input => input.classList.add('d-none'));

                if (type === 'text') {
                    document.getElementById('text-input').classList.remove('d-none');
                } else if (type === 'textarea') {
                    document.getElementById('textarea-input').classList.remove('d-none');
                } else if (type === 'url') {
                    document.getElementById('url-input').classList.remove('d-none');
                } else if (type === 'image') {
                    document.getElementById('image-input').classList.remove('d-none');
                } else {
                    document.getElementById('text-input').classList.remove('d-none');
                }
            }

            showRelevantInput(typeSelect.value);

            // On submit update value
            document.querySelector('form').addEventListener('submit', function (event) {
                updateActualValue();
            });
        });
    </script>
@endpush