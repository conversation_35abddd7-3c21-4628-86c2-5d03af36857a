@extends('layouts.admin')

@section('title', 'Settings')

@section('breadcrumbs')
    <li class="breadcrumb-item active">Settings</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Settings</h3>
                    <div class="card-tools">
                        <a href="{{ route('settings.create') }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-lg"></i> Add New Setting
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <form action="{{ route('settings.index') }}" method="GET" class="form-inline">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="Search settings..."
                                    value="{{ request('search') }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Label</th>
                                    <th>Key</th>
                                    <th>Type</th>
                                    <th>Value</th>
                                    <th>Last Updated</th>
                                    <th>Updated By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($settings as $setting)
                                    <tr>
                                        <td>{{ $setting->label }}</td>
                                        <td><code>{{ $setting->key }}</code></td>
                                        <td>
                                            <span
                                                class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $setting->type)) }}</span>
                                        </td>
                                        <td>
                                            @if($setting->type === 'image')
                                                @if($setting->value)
                                                    <img src="{{ asset('storage/' . $setting->value) }}" alt="{{ $setting->label }}"
                                                        class="img-thumbnail" style="max-height: 50px">
                                                @else
                                                    <span class="text-muted">No image</span>
                                                @endif
                                            @elseif($setting->type === 'textarea')
                                                {{ Str::limit($setting->value, 50) }}
                                            @else
                                                {{ Str::limit($setting->value, 50) }}
                                            @endif
                                        </td>
                                        <td>{{ $setting->updated_at->format('M d, Y H:i') }}</td>
                                        <td>{{ $setting->updatedBy ? $setting->updatedBy->name : 'N/A' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('settings.edit', $setting) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form action="{{ route('settings.destroy', $setting) }}" method="POST"
                                                    class="d-inline"
                                                    onsubmit="return confirm('Are you sure you want to delete this setting?');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">No settings found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        {{ $settings->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection