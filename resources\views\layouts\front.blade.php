<!DOCTYPE html>
<html lang="en">

<head>
    <!-- meta tags -->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="keywords" content="">

    <!-- title -->
    <title>{{ setting('site_title') }} | @yield('title', 'Home')</title>

    <!-- favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('assets/img/logo/favicon.png') }}">

    <!-- css -->
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/all-fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/animate.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/magnific-popup.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/owl.carousel.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/jquery-ui.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/nice-select.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/flex-slider.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}">

    <!-- AddToAny social sharing -->
    <script>
        // AddToAny configuration
        var a2a_config = a2a_config || {};
        a2a_config.onclick = 1; // Enable click tracking
        a2a_config.num_services = 6; // Show 6 services
        a2a_config.color_main = "D7E5ED"; // Use a light blue that matches your theme
        a2a_config.color_border = "AECADB"; // Border color
        a2a_config.color_link_text = "333333"; // Text color
        a2a_config.color_link_text_hover = "333333"; // Text hover color
    </script>
    <script async src="https://static.addtoany.com/menu/page.js"></script>

    <style>
        .theme-btn-enquiry {
            background-color: #4CAF50;
            color: white;
            margin-right: 10px;
        }

        .theme-btn-enquiry:hover {
            background-color: #45a049;
            color: white;
        }

        /* AddToAny styling */
        .a2a_kit {
            display: inline-block;
            line-height: 0;
            vertical-align: middle;
        }

        .shop-single-share .a2a_kit {
            margin-left: 10px;
        }

        .quickview-social .a2a_kit {
            margin-left: 10px;
        }
    </style>


</head>

<body class="home-3">

    <!-- preloader -->
    <div class="preloader">
        <div class="loader-ripple">
            <div></div>
            <div></div>
        </div>
    </div>
    <!-- preloader end -->


    <!-- header area -->
    <header class="header">

        <!-- header top -->
        <div class="header-top">
            <div class="container">
                <div class="header-top-wrapper">
                    <div class="row">
                        <div class="col-12 col-md-6 col-lg-6 col-xl-5">
                            <div class="header-top-left">
                                <ul class="header-top-list">
                                    <li>
                                        <p><i class="far fa-fire"></i> The Biggest Sale Ever 50% Off</p>
                                    </li>
                                    @if (setting('phone_number'))
                                        <li><a href="tel:+{{ str_replace(' ', '', setting('phone_number')) }}"><i
                                                    class="far fa-headset"></i>
                                                {{ setting('phone_number') }}</a>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-6 col-xl-7">
                            <div class="header-top-right">
                                <ul class="header-top-list">

                                    @if (setting('linkdin_url') || setting('instagram_url') || setting('facebook_url') || setting('x_url'))
                                        <li class="social">
                                            <div class="header-top-social">
                                                <span>Follow Us: </span>
                                                @if (setting('facebook_url'))
                                                    <a href="{{ setting('facebook_url') }}"><i class="fab fa-facebook"></i></a>
                                                @endif
                                                @if (setting('x_url'))
                                                    <a href="{{ setting('x_url') }}"><i class="fab fa-x-twitter"></i></a>
                                                @endif
                                                @if (setting('instagram_url'))
                                                    <a href="{{ setting('instagram_url') }}"><i
                                                            class="fab fa-instagram"></i></a>
                                                @endif
                                                @if (setting('linkdin_url'))
                                                    <a href="{{ setting('linkdin_url') }}"><i class="fab fa-linkedin"></i></a>
                                                @endif
                                            </div>
                                        </li>
                                    @endif

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- header top end -->


        <!-- navbar -->
        <div class="main-navigation">
            <nav class="navbar navbar-expand-lg">
                <div class="container position-relative">
                    <h3><a class="navbar-brand" href="{{ route('home') }}">
                            {{ setting('site_title') }}
                        </a></h3>
                    <div class="mobile-menu-right">
                        <div class="mobile-menu-btn">
                            <a href="#" class="nav-right-link search-box-outer"><i class="far fa-search"></i></a>
                            <a href="#" class="nav-right-link" data-bs-toggle="modal" data-bs-target="#enquiryModal">
                                <i class="far fa-envelope"></i>
                            </a>
                            <a href="wishlist.html" class="nav-right-link"><i
                                    class="far fa-heart"></i><span>2</span></a>
                            <a href="shop-cart.html" class="nav-right-link"><i
                                    class="far fa-shopping-bag"></i><span>5</span></a>
                        </div>
                        <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas"
                            data-bs-target="#offcanvasNavbar" aria-controls="offcanvasNavbar"
                            aria-label="Toggle navigation">
                            <span></span>
                            <span></span>
                            <span></span>
                        </button>
                    </div>
                    <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvasNavbar"
                        aria-labelledby="offcanvasNavbarLabel">
                        <div class="offcanvas-header">
                            <a href="index-2.html" class="offcanvas-brand" id="offcanvasNavbarLabel">
                                <img src="{{ asset('assets/img/logo/logo.png') }}" alt="">
                            </a>
                            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"
                                aria-label="Close"></button>
                        </div>
                        <div class="offcanvas-body">
                            <ul class="navbar-nav justify-content-end flex-grow-1 pe-lg-5">
                                <li class="nav-item dropdown">
                                    <a class="nav-link active" href="{{ url('/') }}">Home</a>

                                </li>
                                <!-- <li class="nav-item"><a class="nav-link" href="about.html">About</a></li>


                                                <li class="nav-item dropdown">
                                                    <a class="nav-link" href="shop.html">Shop</a>

                                                </li>
                                                <li class="nav-item dropdown">
                                                    <a class="nav-link " href="#">Blog</a>

                                                </li>
                                                <li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li> -->
                            </ul>
                            <!-- nav-right -->
                            <div class="nav-right">

                                <div class="nav-right-btn">
                                    <a href="#" class="theme-btn theme-btn-enquiry" data-bs-toggle="modal"
                                        data-bs-target="#enquiryModal">
                                        <span class="far fa-envelope"></span> Enquiry
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
        <!-- navbar end -->

    </header>
    <!-- header area end -->


    <!-- popup search -->
    <div class="search-popup">
        <button class="close-search"><span class="far fa-times"></span></button>
        <form action="#">
            <div class="form-group">
                <input type="search" name="search-field" class="form-control" placeholder="Search Here..." required>
                <button type="submit"><i class="far fa-search"></i></button>
            </div>
        </form>
    </div>
    <!-- popup search end -->

    @yield('content')


    <!-- footer area -->
    <footer class="footer-area">
        <div class="footer-widget">
            <div class="container">
                <div class="row footer-widget-wrapper pt-100 pb-40">
                    <?php $isMobileAppName = !!setting('mobile_app_name'); ?>
                    <div class="col-md-6 col-lg-<?= $isMobileAppName ? 4 : 6 ?>">
                        <div class="footer-widget-box about-us">
                            <a href="{{ route('home') }}" class="footer-logo" style="color:#fff">
                                {{ setting('site_title') }}

                            </a>
                            @if (setting('footer_description'))
                                <p class="mb-3">
                                    {{ setting('footer_description') }}
                                </p>
                            @endif
                            <ul class="footer-contact">
                                @if (setting('phone_number'))
                                    <li><a href="tel:{{ str_replace(' ', '', setting('phone_number')) }}"><i
                                                class="far fa-phone"></i>{{ setting('phone_number') }}</a></li>
                                @endif
                                @if (setting('footer_address'))
                                    <li><i class="far fa-map-marker-alt"></i>{{ setting('footer_address') }}</li>
                                @endif
                                @if (setting('email'))
                                    <li><a href="mailto:{{ setting('email') }}"><i
                                                class="far fa-envelope"></i>{{ setting('email') }}</a></li>
                                @endif
                                @if (setting('footer_timing'))
                                    <li><i class="far fa-clock"></i>Mon-Fri (9.00AM - 8.00PM)</li>
                                @endif
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-<?= $isMobileAppName ? 3 : 3 ?>">
                        <div class="footer-widget-box list">
                            <h4 class="footer-widget-title">Quick Links</h4>
                            <ul class="footer-list">
                                <li><a href="{{ route('about') }}">About Us</a></li>
                                <li><a href="{{ route('contact') }}">Contact Us</a></li>
                                <li><a href="{{ route('terms-conditions') }}">Terms & Conditions</a></li>
                                <li><a href="{{ route('privacy-policy') }}">Privacy Policy</a></li>
                                @if(setting('phone_number'))
                                    <li><a href="tel:{{ str_replace(' ', '', setting('phone_number')) }}">Call Us</a>
                                    </li>
                                @endif
                                @if(setting('email'))
                                    <li><a href="mailto:{{ setting('email') }}">Email Us</a></li>
                                @endif
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-<?= $isMobileAppName ? 3 : 3 ?>">
                        <div class="footer-widget-box list">
                            <h4 class="footer-widget-title">Browse Category</h4>
                            <ul class="footer-list">
                                @forelse($footerCategories as $category)
                                    <li><a
                                            href="{{ route('category_products', $category->slug) }}">{{ $category->name }}</a>
                                    </li>
                                @empty
                                    <li>No categories available</li>
                                @endforelse
                            </ul>
                        </div>
                    </div>

                    @if (setting('mobile_app_name'))
                        <div class="col-md-6 col-lg-2">
                            <div class="footer-widget-box list">
                                <h4 class="footer-widget-title">Get Mobile App</h4>
                                <p>{{ setting('mobile_app_name') }} is now available on App Store & Google Play.</p>
                                <div class="footer-download">
                                    <h5>Download Our Mobile App</h5>
                                    <div class="footer-download-btn">
                                        @if (setting('android_app'))
                                            <a href="{{ setting('android_app') }}">
                                                <i class="fab fa-google-play"></i>
                                                <div class="download-btn-info">
                                                    <span>Get It On</span>
                                                    <h6>Google Play</h6>
                                                </div>
                                            </a>
                                        @endif
                                        @if (setting('ios_app'))
                                            <a href="{{ setting('ios_app') }}">
                                                <i class="fab fa-app-store"></i>
                                                <div class="download-btn-info">
                                                    <span>Get It On</span>
                                                    <h6>App Store</h6>
                                                </div>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="copyright">
            <div class="container">
                <div class="copyright-wrap">
                    <div class="row">
                        <div class="col-12 col-lg-6 align-self-center">
                            <p class="copyright-text">
                                &copy; Copyright <span id="date"></span> <a href="{{ route('home') }}">
                                    {{ setting('site_title') }}
                                </a> All Rights Reserved.
                            </p>
                        </div>
                        @if (setting('linkdin_url') || setting('instagram_url') || setting('facebook_url') || setting('x_url'))
                            <div class="col-12 col-lg-6 align-self-center">
                                <div class="footer-social">
                                    <span>Follow Us:</span>
                                    @if (setting('facebook_url'))
                                        <a href="{{ setting('facebook_url') }}"><i class="fab fa-facebook-f"></i></a>
                                    @endif
                                    @if (setting('x_url'))
                                        <a href="{{ setting('x_url') }}"><i class="fab fa-x-twitter"></i></a>
                                    @endif
                                    @if (setting('instagram_url'))
                                        <a href="{{ setting('instagram_url') }}"><i class="fab fa-linkedin-in"></i></a>
                                    @endif
                                    @if (setting('linkdin_url'))
                                        <a href="{{ setting('linkdin_url') }}"><i class="fab fa-youtube"></i></a>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- footer area end -->


    <!-- scroll-top -->
    <a href="#" id="scroll-top"><i class="far fa-arrow-up-from-arc"></i></a>
    <!-- scroll-top end -->


    <!-- modal quick shop-->
    <div class="modal quickview fade" id="quickview" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="quickview" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class="far fa-xmark"></i></button>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                            <img src="{{ asset('assets/img/product/04.png') }}" alt="#" id="quickview-image">
                        </div>
                        <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                            <div class="quickview-content">
                                <h4 class="quickview-title">Simple Denim Chair</h4>
                                <div class="quickview-price">
                                    <h5><span>₹740</span></h5>
                                </div>
                                <div class="product-description mt-3 mb-3">
                                    <p id="product-description">Product description will appear here.</p>
                                </div>
                                <div class="quickview-cart">
                                    <a href="#" class="theme-btn" id="view-product-btn">View Product</a>
                                </div>
                                <div class="quickview-social">
                                    <span>Share:</span>
                                    <!-- AddToAny BEGIN -->
                                    <div class="a2a_kit a2a_kit_size_32 a2a_default_style" id="quickview-share-buttons">
                                        <a class="a2a_button_facebook"></a>
                                        <a class="a2a_button_twitter"></a>
                                        <a class="a2a_button_linkedin"></a>
                                        <a class="a2a_button_pinterest"></a>
                                        <a class="a2a_button_whatsapp"></a>
                                        <a class="a2a_button_email"></a>
                                    </div>
                                    <!-- AddToAny END -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- modal quick shop end -->

    <!-- Enquiry Modal -->
    <div class="modal fade" id="enquiryModal" tabindex="-1" aria-labelledby="enquiryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="enquiryModalLabel">Product Enquiry</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('enquiry.store') }}" method="POST" id="enquiryForm">
                        @csrf
                        <input type="hidden" name="product_id" id="enquiry_product_id" value="">

                        <div class="mb-3">
                            <label for="name" class="form-label">Your Name *</label>
                            <input type="text" class="form-control" id="enquiry_name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="enquiry_email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="text" class="form-control" id="enquiry_phone" name="phone">
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject *</label>
                            <input type="text" class="form-control" id="enquiry_subject" name="subject" required>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Your Message *</label>
                            <textarea class="form-control" id="enquiry_message" name="message" rows="4"
                                required></textarea>
                        </div>

                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn theme-btn">Send Enquiry</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- js -->
    <script data-cfasync="false" src="../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
    <script src="{{ asset('assets/js/jquery-3.7.1.min.js') }}"></script>
    <script src="{{ asset('assets/js/modernizr.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/js/imagesloaded.pkgd.min.js') }}"></script>
    <script src="{{ asset('assets/js/jquery.magnific-popup.min.js') }}"></script>
    <script src="{{ asset('assets/js/isotope.pkgd.min.js') }}"></script>
    <script src="{{ asset('assets/js/jquery.appear.min.js') }}"></script>
    <script src="{{ asset('assets/js/jquery.easing.min.js') }}"></script>
    <script src="{{ asset('assets/js/owl.carousel.min.js') }}"></script>
    <script src="{{ asset('assets/js/counter-up.js') }}"></script>
    <script src="{{ asset('assets/js/jquery-ui.min.js') }}"></script>
    <script src="{{ asset('assets/js/jquery.nice-select.min.js') }}"></script>
    <script src="{{ asset('assets/js/countdown.min.js') }}"></script>
    <script src="{{ asset('assets/js/wow.min.js') }}"></script>
    <script src="{{ asset('assets/js/flex-slider.js') }}"></script>
    <script src="{{ asset('assets/js/main.js') }}"></script>

    <script>
        $(document).ready(function () {
            $('.quickview').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var json = button.data('json');

                // Create a textarea element to decode HTML entities
                var textarea = document.createElement('textarea');
                textarea.innerHTML = json;
                var decodedJson = textarea.value;

                try {
                    var product = JSON.parse(decodedJson);
                    console.log(product);

                    // Update modal content with product data
                    var modal = $(this);
                    modal.find('.quickview-title').text(product.name);

                    // Update price
                    if (product.price && product.price != 0) {
                        modal.find('.quickview-price h5 span').text('₹' + product.price);
                    } else {
                        modal.find('.quickview-price h5 span').text('');
                    }

                    // Update product description
                    if (product.description) {
                        modal.find('#product-description').html(product.description);
                    } else {
                        modal.find('#product-description').html('No description available for this product.');
                    }

                    // Update product image if available
                    if (product.primary_image && product.primary_image.path) {
                        modal.find('#quickview-image').attr('src', '{{ asset("storage") }}/' + product.primary_image.path);
                        modal.find('#quickview-image').attr('alt', product.name);
                    } else {
                        modal.find('#quickview-image').attr('src', '{{ asset("assets/img/product/04.png") }}');
                        modal.find('#quickview-image').attr('alt', product.name);
                    }

                    // Set the View Product button URL
                    if (product.slug) {
                        var productUrl = '{{ url("product") }}/' + product.slug;
                        modal.find('#view-product-btn').attr('href', productUrl);

                        // Update AddToAny configuration for the current product
                        var productName = product.name;
                        var imageUrl = product.primary_image && product.primary_image.path
                            ? '{{ asset("storage") }}/' + product.primary_image.path
                            : '{{ asset("assets/img/product/04.png") }}';

                        // Update AddToAny configuration
                        if (typeof a2a !== 'undefined') {
                            a2a.init('page');  // Reinitialize AddToAny

                            // Set the URL and title for sharing
                            a2a_config.linkurl = productUrl;
                            a2a_config.linkname = productName;

                            // Force AddToAny to update its buttons
                            try {
                                a2a.update();
                            } catch (e) {
                                console.log('AddToAny update error:', e);
                            }
                        }
                    }
                } catch (e) {
                    console.error('Error parsing JSON:', e);
                }
            });
        });
    </script>

    @yield('scripts')
    @stack('scripts')

    <script>
        $(document).ready(function () {
            // Load saved form data from localStorage when the modal is shown
            $('#enquiryModal').on('show.bs.modal', function (e) {
                var button = $(e.relatedTarget);
                var productId = button.data('product-id');

                if (productId) {
                    $('#enquiry_product_id').val(productId);
                }

                // Load saved form data from localStorage
                if (localStorage.getItem('enquiry_name')) {
                    $('#enquiry_name').val(localStorage.getItem('enquiry_name'));
                }
                if (localStorage.getItem('enquiry_email')) {
                    $('#enquiry_email').val(localStorage.getItem('enquiry_email'));
                }
                if (localStorage.getItem('enquiry_phone')) {
                    $('#enquiry_phone').val(localStorage.getItem('enquiry_phone'));
                }
            });

            // Save form data to localStorage when typing
            $('#enquiry_name').on('input', function () {
                localStorage.setItem('enquiry_name', $(this).val());
            });

            $('#enquiry_email').on('input', function () {
                localStorage.setItem('enquiry_email', $(this).val());
            });

            $('#enquiry_phone').on('input', function () {
                localStorage.setItem('enquiry_phone', $(this).val());
            });

            // Handle form submission
            $('#enquiryForm').on('submit', function (e) {
                e.preventDefault();

                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function (response) {
                        if (response.success) {
                            alert('Your enquiry has been sent successfully!');
                            $('#enquiryModal').modal('hide');

                            // Don't clear localStorage to keep the data for future enquiries
                            // Just reset the form fields that should be cleared
                            $('#enquiry_subject').val('');
                            $('#enquiry_message').val('');
                            $('#enquiry_product_id').val('');
                        } else {
                            alert('There was an error sending your enquiry. Please try again.');
                        }
                    },
                    error: function (xhr) {
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            var errorMessage = '';

                            for (var key in errors) {
                                errorMessage += errors[key][0] + '\n';
                            }

                            alert('Please correct the following errors:\n' + errorMessage);
                        } else {
                            alert('There was an error sending your enquiry. Please try again.');
                        }
                    }
                });
            });
        });
    </script>

</body>

</html>