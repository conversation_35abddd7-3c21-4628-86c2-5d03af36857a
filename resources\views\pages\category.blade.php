@extends('layouts.front')

@section('title', 'Welcome to Our Furniture Store')
@section('meta_description', 'Discover our exclusive collection of high-quality furniture for your home. Find sofas, beds, dining sets, and more at competitive prices.')
@section('meta_keywords', 'furniture, home decor, sofas, beds, dining sets, chairs, tables')

@section('content')

    <main class="main">

        <!-- breadcrumb -->
        <div class="site-breadcrumb">
            <div class="site-breadcrumb-bg" style="background:#20202C"></div>
            <div class="container">
                <div class="site-breadcrumb-wrap">
                    <h4 class="breadcrumb-title">{{ $category->name }}</h4>
                    <ul class="breadcrumb-menu">
                        <li><a href="{{ url('/') }}"><i class="far fa-home"></i> Home</a></li>
                        <li class="active">{{ $category->name }}</li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- breadcrumb end -->


        <!-- shop-area -->
        <div class="shop-area2 py-90">
            <div class="container">
                <div class="row g-4">
                    <div class="col-lg-12">
                        <div class="col-md-12">
                            <div class="shop-sort">
                                <div class="shop-sort-box">
                                    <div class="shop-sorty-label">Sort By:</div>
                                    <select class="select" id="sort-products">
                                        <option value="default" {{ request('sort') == 'default' || !request('sort') ? 'selected' : '' }}>Default Sorting</option>
                                        <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest
                                            Items</option>
                                        <option value="bestseller" {{ request('sort') == 'bestseller' ? 'selected' : '' }}>
                                            Best Seller Items</option>
                                        <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price
                                            - Low To High</option>
                                        <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>
                                            Price - High To Low</option>
                                    </select>
                                    <div class="shop-sort-show">Showing {{ $products->firstItem() ?? 0 }} to
                                        {{ $products->lastItem() ?? 0 }} of {{ $products->total() }} Results
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="shop-item-wrap item-2">
                            <div class="row g-4">

                                @foreach ($products as $product)
                                    <div class="col-md-6 col-lg-3">
                                        <div class="product-item">
                                            <div class="product-img">
                                                <a href="{{ route('product_view', $product->slug) }}">
                                                    @if($product->primary_image)
                                                        <img src="{{ asset('storage/' . $product->primary_image->path) }}"
                                                            alt="{{ $product->name }}">
                                                    @else
                                                        <img src="{{ asset('assets/img/product/04.png') }}"
                                                            alt="{{ $product->name }}">
                                                    @endif
                                                </a>
                                                <div class="product-action-wrap">
                                                    <div class="product-action">
                                                        <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                                            data-json="{{ htmlspecialchars(json_encode($product), ENT_QUOTES, 'UTF-8') }}"
                                                            data-tooltip="tooltip" title="Quick View"><i
                                                                class="far fa-eye"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="product-content">
                                                <h3 class="product-title"><a href="shop-single.html">{{ $product->name }}</a>
                                                </h3>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <!-- pagination -->
                        <div class="pagination-area mt-50">
                            <div aria-label="Page navigation">
                                {{ $products->links('vendor.pagination.bootstrap-5') }}
                            </div>
                            <div class="shop-sort-show mt-3">
                                Showing {{ $products->firstItem() ?? 0 }} to {{ $products->lastItem() ?? 0 }}
                                of {{ $products->total() }} Products
                            </div>
                        </div>
                        <!-- pagination end -->
                    </div>

                </div>
            </div>
        </div>
        <!-- shop-area end -->

    </main>

@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Handle sort dropdown change
            const sortDropdown = document.getElementById('sort-products');
            if (sortDropdown) {
                sortDropdown.addEventListener('change', function () {
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('sort', this.value);
                    window.location.href = currentUrl.toString();
                });
            }
        });
    </script>
@endpush