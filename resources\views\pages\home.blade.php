@extends('layouts.front')

@section('title', 'Welcome to Our Furniture Store')
@section('meta_description', 'Discover our exclusive collection of high-quality furniture for your home. Find sofas, beds, dining sets, and more at competitive prices.')
@section('meta_keywords', 'furniture, home decor, sofas, beds, dining sets, chairs, tables')

@section('content')
    <main class="main">

        <!-- hero slider -->
        <div class="hero-section hs-3">
            <div class="container-fluid px-0">
                <div class="hero-slider owl-carousel owl-theme">
                    @forelse($banners as $banner)
                        <div class="hero-single">
                            <div class="hero-single-bg"
                                style="background-image: url({{ asset('storage/' . $banner->image_link) }})"></div>
                            <div class="container">
                                <div class="row align-items-center">
                                    <div class="col-md-12 col-lg-8 col-xl-6">
                                        <div class="hero-content">
                                            <h6 class="hero-sub-title" data-animation="fadeInUp" data-delay=".25s">
                                                {{ $banner->name }}</h6>
                                            <h1 class="hero-title" data-animation="fadeInRight" data-delay=".50s">
                                                {{ $banner->title }}
                                            </h1>
                                            @if($banner->description)
                                                <p data-animation="fadeInLeft" data-delay=".75s">
                                                    {{ $banner->description }}
                                                </p>
                                            @endif
                                            <div class="hero-btn" data-animation="fadeInUp" data-delay="1s">
                                                @if($banner->shop_now_link)
                                                    <a href="{{ $banner->shop_now_link }}" class="theme-btn">Shop Now<i
                                                            class="fas fa-arrow-right"></i></a>
                                                @endif
                                                <a href="{{ route('about') }}" class="theme-btn theme-btn2">Learn More<i
                                                        class="fas fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <!-- Default banners if no banners are available in the database -->
                        <div class="hero-single">
                            <div class="hero-single-bg"
                                style="background-image: url({{ asset('assets/img/hero/slider-1.jpg') }})"></div>
                            <div class="container">
                                <div class="row align-items-center">
                                    <div class="col-md-12 col-lg-8 col-xl-6">
                                        <div class="hero-content">
                                            <h6 class="hero-sub-title" data-animation="fadeInUp" data-delay=".25s">Welcome
                                                to Furniture
                                                !</h6>
                                            <h1 class="hero-title" data-animation="fadeInRight" data-delay=".50s">
                                                Discover modern <span>furniture</span> for your every room
                                            </h1>
                                            <p data-animation="fadeInLeft" data-delay=".75s">
                                                There are many variations of passages orem psum available but the majority
                                                have
                                                suffered are going to use a passage alteration in some form by injected
                                                humour.
                                            </p>
                                            <div class="hero-btn" data-animation="fadeInUp" data-delay="1s">
                                                <a href="#" class="theme-btn">Shop Now<i class="fas fa-arrow-right"></i></a>
                                                <a href="{{ route('about') }}" class="theme-btn theme-btn2">Learn More<i
                                                        class="fas fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
        <!-- hero slider end -->

        <!-- category area -->
        <div class="category-area3 pt-50">
            <div class="container wow fadeInUp" data-wow-delay=".25s">
                <div class="category-slider owl-carousel owl-theme">

                    @foreach($categories as $category)
                        <div class="category-item">
                            <a href="{{ route('category_products', $category->slug) }}">
                                <div class="category-info">
                                    <div class="icon">
                                        @if($category->images->isNotEmpty())
                                            <img src="{{ asset('storage/' . $category->images->first()->path) }}"
                                                alt="{{ $category->name }}">
                                        @else
                                            <img src="{{ asset('assets/img/category/default.png') }}" alt="{{ $category->name }}">
                                        @endif
                                    </div>
                                    <div class="content">
                                        <h4>{{ $category->name }}</h4>
                                        <p>{{ $category->products_count }} Items</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        <!-- category area end-->

        <!-- listings sections -->
        @foreach($listings as $listing)
            <div class="product-area pb-50 mt-5">
                <div class="container">
                    <div class="row">
                        <div class="col-12 wow fadeInDown" data-wow-delay=".25s">
                            <div class="site-heading-inline">
                                <h2 class="site-title">{{ $listing->name }}</h2>
                                <a href="{{ route('listing_products', $listing->slug) }}">View More <i
                                        class="fas fa-angle-double-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="product-wrap item-2 wow fadeInUp" data-wow-delay=".25s">
                        <div class="product-slider owl-carousel owl-theme">
                            @foreach($listing->products as $product)
                                <div class="product-item">
                                    <div class="product-img">
                                        @if($product->price < 200)
                                            <span class="type discount">Sale</span>
                                        @elseif($loop->first)
                                            <span class="type new">New</span>
                                        @elseif($loop->iteration == 2)
                                            <span class="type hot">Hot</span>
                                        @endif
                                        <a href="{{ route('product_view', $product->slug) }}">
                                            @if($product->images->isNotEmpty())
                                                <img src="{{ asset('storage/' . $product->images->first()->path) }}"
                                                    alt="{{ $product->name }}">
                                            @else
                                                <img src="{{ asset('assets/img/product/01.png') }}" alt="{{ $product->name }}">
                                            @endif
                                        </a>
                                        <div class="product-action-wrap">
                                            <div class="product-action">
                                                <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                                    data-tooltip="tooltip" title="Quick View"><i class="far fa-eye"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="product-content">
                                        <h3 class="product-title"><a
                                                href="{{ route('product_view', $product->slug) }}">{{ $product->name }}</a></h3>
                                        <div class="product-rate">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                        <div class="product-bottom">
                                            <div class="product-price">
                                                <span>${{ number_format($product->price, 2) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
        <!-- listings sections end -->

        <!-- choose-area -->
        <div class="choose-area pb-100">
            <div class="container">
                <div class="row g-4 align-items-center wow fadeInDown" data-wow-delay=".25s">
                    <div class="col-lg-4">
                        <span class="site-title-tagline">Why Choose Us</span>
                        <h2 class="site-title">We Provide Premium Quality Furniture For You</h2>
                    </div>
                    <div class="col-lg-4">
                        <p>There are many variations of passages available but the majority have suffered you are going
                            to use a passage you need to be sure alteration in some form by injected humour randomised
                            words even slightly believable.</p>
                    </div>
                    <div class="col-lg-4">
                        <div class="choose-img">
                            <img src="{{ asset('assets/img/choose/01.jpg') }}" alt="">
                        </div>
                    </div>
                </div>
                <div class="choose-content wow fadeInUp" data-wow-delay=".25s">
                    <div class="row g-4">
                        <div class="col-lg-4">
                            <div class="choose-item">
                                <div class="choose-icon">
                                    <img src="{{ asset('assets/img/icon/warranty.svg') }}" alt="">
                                </div>
                                <div class="choose-info">
                                    <h4>3 Years Warranty</h4>
                                    <p>It is a long established fact that a reader will be distracted by the readable
                                        content of a page when looking at its layout.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="choose-item">
                                <div class="choose-icon">
                                    <img src="{{ asset('assets/img/icon/price.svg') }}" alt="">
                                </div>
                                <div class="choose-info">
                                    <h4>Affordable Price</h4>
                                    <p>It is a long established fact that a reader will be distracted by the readable
                                        content of a page when looking at its layout.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="choose-item">
                                <div class="choose-icon">
                                    <img src="{{ asset('assets/img/icon/delivery.svg') }}" alt="">
                                </div>
                                <div class="choose-info">
                                    <h4>Free Shipping</h4>
                                    <p>It is a long established fact that a reader will be distracted by the readable
                                        content of a page when looking at its layout.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- choose-area end-->

    </main>
@endsection