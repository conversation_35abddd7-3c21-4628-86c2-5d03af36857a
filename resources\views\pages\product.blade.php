@extends('layouts.front')

@section('title', 'Welcome to Our Furniture Store')
@section('meta_description', 'Discover our exclusive collection of high-quality furniture for your home. Find sofas, beds, dining sets, and more at competitive prices.')
@section('meta_keywords', 'furniture, home decor, sofas, beds, dining sets, chairs, tables')

@section('content')
    <main class="main">

        <!-- breadcrumb -->
        <div class="site-breadcrumb">
            <div class="site-breadcrumb-bg" style="background: #20202C"></div>
            <div class="container">
                <div class="site-breadcrumb-wrap">
                    <h4 class="breadcrumb-title">{{ $product->name }}</h4>
                    <ul class="breadcrumb-menu">
                        <li><a href="{{ url('/') }}"><i class="far fa-home"></i> Home</a></li>
                        <li class="active">{{ $product->name }}</li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- breadcrumb end -->


        <!-- shop single -->
        <div class="shop-single py-90">
            <div class="container">
                <div class="row">
                    <div class="col-md-9 col-lg-6 col-xxl-5">
                        <div class="shop-single-gallery">
                            @if($product->youtube_video_url)
                                <a class="shop-single-video popup-youtube" href="{{ $product->youtube_video_url }}"
                                    data-tooltip="tooltip" title="Watch Video">
                                    <i class="far fa-play"></i>
                                </a>
                            @endif
                            <div class="flexslider-thumbnails">
                                <ul class="slides">
                                    @if($product->images->count() > 0)
                                        @foreach($product->images as $image)
                                            <li data-thumb="{{ asset("storage/{$image->path}") }}" rel="adjustX:10, adjustY:">
                                                <img src="{{ asset("storage/{$image->path}") }}" alt="{{ $product->name }}">
                                            </li>
                                        @endforeach
                                    @else
                                        <li data-thumb="{{ asset('assets/img/product/01.png') }}" rel="adjustX:10, adjustY:">
                                            <img src="{{ asset('assets/img/product/01.png') }}" alt="{{ $product->name }}">
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 col-lg-6 col-xxl-6">
                        <div class="shop-single-info">
                            <h4 class="shop-single-title">{{ $product->name }}</h4>
                            <div class="shop-single-price">
                                @if ($product->price > 0)
                                    <span class="amount">₹{{ $product->price }}</span>
                                @endif
                            </div>
                            <div class="short-description mb-3">
                                @php
                                    // Extract first paragraph or first 150 characters as short description
                                    $description = strip_tags($product->description);
                                    $shortDesc = '';

                                    if (strpos($description, '.') !== false) {
                                        // Get first sentence
                                        $shortDesc = substr($description, 0, strpos($description, '.') + 1);
                                    } else {
                                        // Or get first 150 characters
                                        $shortDesc = substr($description, 0, 150);
                                        if (strlen($description) > 150) {
                                            $shortDesc .= '...';
                                        }
                                    }
                                @endphp
                                <p>{{ $shortDesc }}</p>
                            </div>

                            <div class="shop-single-action">
                                <div class="row align-items-center">
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="shop-single-btn">
                                            <a href="#" class="theme-btn theme-btn-enquiry" data-bs-toggle="modal"
                                                data-bs-target="#enquiryModal" data-product-id="{{ $product->id }}">
                                                <span class="far fa-envelope"></span>Enquiry
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="shop-single-share">
                                            <span>Share:</span>
                                            <!-- AddToAny BEGIN -->
                                            <div class="a2a_kit a2a_kit_size_32 a2a_default_style"
                                                data-a2a-url="{{ route('product_view', $product->slug) }}"
                                                data-a2a-title="{{ $product->name }}">
                                                <a class="a2a_button_facebook"></a>
                                                <a class="a2a_button_twitter"></a>
                                                <a class="a2a_button_linkedin"></a>
                                                <a class="a2a_button_pinterest"></a>
                                                <a class="a2a_button_whatsapp"></a>
                                                <a class="a2a_button_email"></a>
                                            </div>
                                            <!-- AddToAny END -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- shop single details -->
                <div class="shop-single-details">
                    <nav>
                        <div class="nav nav-tabs" id="nav-tab" role="tablist">
                            <button class="nav-link active" id="nav-tab1" data-bs-toggle="tab" data-bs-target="#tab1"
                                type="button" role="tab" aria-controls="tab1" aria-selected="true">Description</button>
                        </div>
                    </nav>
                    <div class="tab-content" id="nav-tabContent">
                        <div class="tab-pane fade show active" id="tab1" role="tabpanel" aria-labelledby="nav-tab1">
                            <div class="shop-single-desc">
                                {!! $product->description !!}
                            </div>
                        </div>

                    </div>
                </div>
                <!-- shop single details end -->


                <!-- related item -->
                <div class="product-area related-item pt-40">
                    <div class="container px-0">
                        <div class="row">
                            <div class="col-12">
                                <div class="site-heading-inline">
                                    <h2 class="site-title">Related Items</h2>
                                    @if($product->categories->isNotEmpty())
                                        <a href="{{ route('category_products', $product->categories->first()->slug) }}">View
                                            More <i class="fas fa-arrow-right"></i></a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="row g-4 item-2">
                            @forelse($relatedProducts as $relatedProduct)
                                <div class="col-md-6 col-lg-3">
                                    <div class="product-item">
                                        <div class="product-img">
                                            <a href="{{ route('product_view', $relatedProduct->slug) }}">
                                                @if($relatedProduct->primary_image)
                                                    <img src="{{ asset('storage/' . $relatedProduct->primary_image->path) }}"
                                                        alt="{{ $relatedProduct->name }}">
                                                @else
                                                    <img src="{{ asset('assets/img/product/04.png') }}"
                                                        alt="{{ $relatedProduct->name }}">
                                                @endif
                                            </a>
                                            <div class="product-action-wrap">
                                                <div class="product-action">
                                                    <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                                        data-json="{{ json_encode($relatedProduct) }}" data-bs-placement="right"
                                                        data-tooltip="tooltip" title="Quick View"><i class="far fa-eye"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="product-content">
                                            <h3 class="product-title"><a
                                                    href="{{ route('product_view', $relatedProduct->slug) }}">{{ $relatedProduct->name }}</a>
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-12">
                                    <p class="text-center">No related products found.</p>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
                <!-- related item end -->
            </div>
        </div>
        <!-- shop single end -->

    </main>
@endsection